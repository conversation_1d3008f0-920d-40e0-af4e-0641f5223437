{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/platform.mjs"], "sourcesContent": ["export { P as Platform } from './platform-DmdVEw_C.mjs';\nimport * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { n as normalizePassiveListenerOptions, s as supportsPassiveEventListeners } from './passive-listeners-esHZRgIN.mjs';\nexport { R as RtlScrollAxisType, g as getRtlScrollAxisType, s as supportsScrollBehavior } from './scrolling-BkvA05C8.mjs';\nexport { _ as _getEventTarget, c as _getFocusedElementPierceShadowDom, a as _getShadowRoot, b as _supportsShadowDom } from './shadow-dom-B0oHn41l.mjs';\nexport { _ as _isTestEnvironment } from './test-environment-CT0XxPyp.mjs';\nexport { _ as _bindEventWithOptions } from './backwards-compatibility-DHR38MsD.mjs';\nimport '@angular/common';\nclass PlatformModule {\n  static ɵfac = function PlatformModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: PlatformModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes;\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n// `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n// first changing it to something else:\n// The specified value \"\" does not conform to the required format.\n// The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n'color', 'button', 'checkbox', 'date', 'datetime-local', 'email', 'file', 'hidden', 'image', 'month', 'number', 'password', 'radio', 'range', 'reset', 'search', 'submit', 'tel', 'text', 'time', 'url', 'week'];\n/** @returns The input types supported by this browser. */\nfunction getSupportedInputTypes() {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(candidateInputTypes.filter(value => {\n    featureTestInput.setAttribute('type', value);\n    return featureTestInput.type === value;\n  }));\n  return supportedInputTypes;\n}\nexport { PlatformModule, getSupportedInputTypes };\n"], "mappings": ";;;;;;;;AASA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,OAAO,OAAO,SAAS,uBAAuB,mBAAmB;AAC/D,WAAO,KAAK,qBAAqB,iBAAgB;AAAA,EACnD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAGH,IAAI;AAEJ,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAkB;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAY;AAAA,EAAS;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AAAA,EAAU;AAAA,EAAO;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAO;AAAM;AAE/M,SAAS,yBAAyB;AAEhC,MAAI,qBAAqB;AACvB,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU;AAC7C,0BAAsB,IAAI,IAAI,mBAAmB;AACjD,WAAO;AAAA,EACT;AACA,MAAI,mBAAmB,SAAS,cAAc,OAAO;AACrD,wBAAsB,IAAI,IAAI,oBAAoB,OAAO,WAAS;AAChE,qBAAiB,aAAa,QAAQ,KAAK;AAC3C,WAAO,iBAAiB,SAAS;AAAA,EACnC,CAAC,CAAC;AACF,SAAO;AACT;", "names": []}