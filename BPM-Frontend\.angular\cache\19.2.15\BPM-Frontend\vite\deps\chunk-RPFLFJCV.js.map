{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/observers/private.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, Ng<PERSON><PERSON>, RendererFactory2, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * <PERSON><PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = e => {\n  if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n    console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n  }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n  _box;\n  /** Stream that emits when the shared observer is destroyed. */\n  _destroyed = new Subject();\n  /** Stream of all events from the ResizeObserver. */\n  _resizeSubject = new Subject();\n  /** ResizeObserver used to observe element resize events. */\n  _resizeObserver;\n  /** A map of elements to streams of their resize events. */\n  _elementObservables = new Map();\n  constructor(/** The box type to observe for resizes. */\n  _box) {\n    this._box = _box;\n    if (typeof ResizeObserver !== 'undefined') {\n      this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n    }\n  }\n  /**\n   * Gets a stream of resize events for the given element.\n   * @param target The element to observe.\n   * @return The stream of resize events for the element.\n   */\n  observe(target) {\n    if (!this._elementObservables.has(target)) {\n      this._elementObservables.set(target, new Observable(observer => {\n        const subscription = this._resizeSubject.subscribe(observer);\n        this._resizeObserver?.observe(target, {\n          box: this._box\n        });\n        return () => {\n          this._resizeObserver?.unobserve(target);\n          subscription.unsubscribe();\n          this._elementObservables.delete(target);\n        };\n      }).pipe(filter(entries => entries.some(entry => entry.target === target)),\n      // Share a replay of the last event so that subsequent calls to observe the same element\n      // receive initial sizing info like the first one. Also enable ref counting so the\n      // element will be automatically unobserved when there are no more subscriptions.\n      shareReplay({\n        bufferSize: 1,\n        refCount: true\n      }), takeUntil(this._destroyed)));\n    }\n    return this._elementObservables.get(target);\n  }\n  /** Destroys this instance. */\n  destroy() {\n    this._destroyed.next();\n    this._destroyed.complete();\n    this._resizeSubject.complete();\n    this._elementObservables.clear();\n  }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n  _cleanupErrorListener;\n  /** Map of box type to shared resize observer. */\n  _observers = new Map();\n  /** The Angular zone. */\n  _ngZone = inject(NgZone);\n  constructor() {\n    if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      this._ngZone.runOutsideAngular(() => {\n        const renderer = inject(RendererFactory2).createRenderer(null, null);\n        this._cleanupErrorListener = renderer.listen('window', 'error', loopLimitExceededErrorHandler);\n      });\n    }\n  }\n  ngOnDestroy() {\n    for (const [, observer] of this._observers) {\n      observer.destroy();\n    }\n    this._observers.clear();\n    this._cleanupErrorListener?.();\n  }\n  /**\n   * Gets a stream of resize events for the given target element and box type.\n   * @param target The element to observe for resizes.\n   * @param options Options to pass to the `ResizeObserver`\n   * @return The stream of resize events for the element.\n   */\n  observe(target, options) {\n    const box = options?.box || 'content-box';\n    if (!this._observers.has(box)) {\n      this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n    }\n    return this._observers.get(box).observe(target);\n  }\n  static ɵfac = function SharedResizeObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SharedResizeObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: SharedResizeObserver,\n    factory: SharedResizeObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nexport { SharedResizeObserver };\n"], "mappings": ";;;;;;;;;;;;;;;;;AAUA,IAAM,gCAAgC,OAAK;AACzC,MAAI,aAAa,cAAc,EAAE,YAAY,sCAAsC;AACjF,YAAQ,MAAM,GAAG,EAAE,OAAO,8IAA8I;AAAA,EAC1K;AACF;AAKA,IAAM,gCAAN,MAAoC;AAAA,EAClC;AAAA;AAAA,EAEA,aAAa,IAAI,QAAQ;AAAA;AAAA,EAEzB,iBAAiB,IAAI,QAAQ;AAAA;AAAA,EAE7B;AAAA;AAAA,EAEA,sBAAsB,oBAAI,IAAI;AAAA,EAC9B,YACA,MAAM;AACJ,SAAK,OAAO;AACZ,QAAI,OAAO,mBAAmB,aAAa;AACzC,WAAK,kBAAkB,IAAI,eAAe,aAAW,KAAK,eAAe,KAAK,OAAO,CAAC;AAAA,IACxF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,QAAQ;AACd,QAAI,CAAC,KAAK,oBAAoB,IAAI,MAAM,GAAG;AACzC,WAAK,oBAAoB,IAAI,QAAQ,IAAI,WAAW,cAAY;AAC9D,cAAM,eAAe,KAAK,eAAe,UAAU,QAAQ;AAC3D,aAAK,iBAAiB,QAAQ,QAAQ;AAAA,UACpC,KAAK,KAAK;AAAA,QACZ,CAAC;AACD,eAAO,MAAM;AACX,eAAK,iBAAiB,UAAU,MAAM;AACtC,uBAAa,YAAY;AACzB,eAAK,oBAAoB,OAAO,MAAM;AAAA,QACxC;AAAA,MACF,CAAC,EAAE;AAAA,QAAK,OAAO,aAAW,QAAQ,KAAK,WAAS,MAAM,WAAW,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,QAIxE,YAAY;AAAA,UACV,YAAY;AAAA,UACZ,UAAU;AAAA,QACZ,CAAC;AAAA,QAAG,UAAU,KAAK,UAAU;AAAA,MAAC,CAAC;AAAA,IACjC;AACA,WAAO,KAAK,oBAAoB,IAAI,MAAM;AAAA,EAC5C;AAAA;AAAA,EAEA,UAAU;AACR,SAAK,WAAW,KAAK;AACrB,SAAK,WAAW,SAAS;AACzB,SAAK,eAAe,SAAS;AAC7B,SAAK,oBAAoB,MAAM;AAAA,EACjC;AACF;AAWA,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB;AAAA;AAAA,EAEA,aAAa,oBAAI,IAAI;AAAA;AAAA,EAErB,UAAU,OAAO,MAAM;AAAA,EACvB,cAAc;AACZ,QAAI,OAAO,mBAAmB,gBAAgB,OAAO,cAAc,eAAe,YAAY;AAC5F,WAAK,QAAQ,kBAAkB,MAAM;AACnC,cAAM,WAAW,OAAO,gBAAgB,EAAE,eAAe,MAAM,IAAI;AACnE,aAAK,wBAAwB,SAAS,OAAO,UAAU,SAAS,6BAA6B;AAAA,MAC/F,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,cAAc;AACZ,eAAW,CAAC,EAAE,QAAQ,KAAK,KAAK,YAAY;AAC1C,eAAS,QAAQ;AAAA,IACnB;AACA,SAAK,WAAW,MAAM;AACtB,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,QAAQ,SAAS;AACvB,UAAM,MAAM,SAAS,OAAO;AAC5B,QAAI,CAAC,KAAK,WAAW,IAAI,GAAG,GAAG;AAC7B,WAAK,WAAW,IAAI,KAAK,IAAI,8BAA8B,GAAG,CAAC;AAAA,IACjE;AACA,WAAO,KAAK,WAAW,IAAI,GAAG,EAAE,QAAQ,MAAM;AAAA,EAChD;AAAA,EACA,OAAO,OAAO,SAAS,6BAA6B,mBAAmB;AACrE,WAAO,KAAK,qBAAqB,uBAAsB;AAAA,EACzD;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,sBAAqB;AAAA,IAC9B,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;", "names": []}