{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n  return operate((source, subscriber) => {\n    let connection = null;\n    source._refCount++;\n    const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n      if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n        connection = null;\n        return;\n      }\n      const sharedConnection = source._connection;\n      const conn = connection;\n      connection = null;\n      if (sharedConnection && (!conn || sharedConnection === conn)) {\n        sharedConnection.unsubscribe();\n      }\n      subscriber.unsubscribe();\n    });\n    source.subscribe(refCounter);\n    if (!refCounter.closed) {\n      connection = source.connect();\n    }\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "refCount", "source", "subscriber", "connection", "_refCount", "refCounter", "undefined", "sharedConnection", "_connection", "conn", "unsubscribe", "subscribe", "closed", "connect"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/rxjs/dist/esm/internal/operators/refCount.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function refCount() {\n    return operate((source, subscriber) => {\n        let connection = null;\n        source._refCount++;\n        const refCounter = createOperatorSubscriber(subscriber, undefined, undefined, undefined, () => {\n            if (!source || source._refCount <= 0 || 0 < --source._refCount) {\n                connection = null;\n                return;\n            }\n            const sharedConnection = source._connection;\n            const conn = connection;\n            connection = null;\n            if (sharedConnection && (!conn || sharedConnection === conn)) {\n                sharedConnection.unsubscribe();\n            }\n            subscriber.unsubscribe();\n        });\n        source.subscribe(refCounter);\n        if (!refCounter.closed) {\n            connection = source.connect();\n        }\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,QAAQA,CAAA,EAAG;EACvB,OAAOF,OAAO,CAAC,CAACG,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,UAAU,GAAG,IAAI;IACrBF,MAAM,CAACG,SAAS,EAAE;IAClB,MAAMC,UAAU,GAAGN,wBAAwB,CAACG,UAAU,EAAEI,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAE,MAAM;MAC3F,IAAI,CAACL,MAAM,IAAIA,MAAM,CAACG,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,EAAEH,MAAM,CAACG,SAAS,EAAE;QAC5DD,UAAU,GAAG,IAAI;QACjB;MACJ;MACA,MAAMI,gBAAgB,GAAGN,MAAM,CAACO,WAAW;MAC3C,MAAMC,IAAI,GAAGN,UAAU;MACvBA,UAAU,GAAG,IAAI;MACjB,IAAII,gBAAgB,KAAK,CAACE,IAAI,IAAIF,gBAAgB,KAAKE,IAAI,CAAC,EAAE;QAC1DF,gBAAgB,CAACG,WAAW,CAAC,CAAC;MAClC;MACAR,UAAU,CAACQ,WAAW,CAAC,CAAC;IAC5B,CAAC,CAAC;IACFT,MAAM,CAACU,SAAS,CAACN,UAAU,CAAC;IAC5B,IAAI,CAACA,UAAU,CAACO,MAAM,EAAE;MACpBT,UAAU,GAAGF,MAAM,CAACY,OAAO,CAAC,CAAC;IACjC;EACJ,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}