{"ast": null, "code": "import { zip } from './zip';\nexport function zipWith(...otherInputs) {\n  return zip(...otherInputs);\n}", "map": {"version": 3, "names": ["zip", "zipWith", "otherInputs"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/rxjs/dist/esm/internal/operators/zipWith.js"], "sourcesContent": ["import { zip } from './zip';\nexport function zipWith(...otherInputs) {\n    return zip(...otherInputs);\n}\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,OAAO;AAC3B,OAAO,SAASC,OAAOA,CAAC,GAAGC,WAAW,EAAE;EACpC,OAAOF,GAAG,CAAC,GAAGE,WAAW,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}