{"ast": null, "code": "export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:63668',\n  appName: 'BPM Light',\n  version: '1.0.0',\n  enableLogging: true,\n  tokenExpirationTime: 3600000,\n  // 1 hour in milliseconds\n  signalRUrl: 'https://localhost:63668/notificationHub'\n};", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "appName", "version", "enableLogging", "tokenExpirationTime", "signalRUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\bpm project\\BPM-Frontend\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'https://localhost:63668',\n  appName: 'BPM Light',\n  version: '1.0.0',\n  enableLogging: true,\n  tokenExpirationTime: 3600000, // 1 hour in milliseconds\n  signalRUrl: 'https://localhost:63668/notificationHub'\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,yBAAyB;EACjCC,OAAO,EAAE,WAAW;EACpBC,OAAO,EAAE,OAAO;EAChBC,aAAa,EAAE,IAAI;EACnBC,mBAAmB,EAAE,OAAO;EAAE;EAC9BC,UAAU,EAAE;CACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}