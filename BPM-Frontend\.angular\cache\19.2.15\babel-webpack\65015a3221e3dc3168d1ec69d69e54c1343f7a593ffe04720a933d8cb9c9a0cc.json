{"ast": null, "code": "\"use strict\";\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};", "map": {"version": 3, "names": ["module", "exports", "cssWithMappingToString", "list", "toString", "map", "item", "content", "<PERSON><PERSON><PERSON>er", "concat", "length", "join", "i", "modules", "media", "dedupe", "supports", "layer", "undefined", "alreadyImportedModules", "k", "id", "_k", "push"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/css-loader/dist/runtime/api.js"], "sourcesContent": ["\"use strict\";\n\n/*\n  MIT License http://www.opensource.org/licenses/mit-license.php\n  Author <PERSON> @sokra\n*/\nmodule.exports = function (cssWithMappingToString) {\n  var list = [];\n\n  // return the list of modules as css string\n  list.toString = function toString() {\n    return this.map(function (item) {\n      var content = \"\";\n      var needLayer = typeof item[5] !== \"undefined\";\n      if (item[4]) {\n        content += \"@supports (\".concat(item[4], \") {\");\n      }\n      if (item[2]) {\n        content += \"@media \".concat(item[2], \" {\");\n      }\n      if (needLayer) {\n        content += \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\");\n      }\n      content += cssWithMappingToString(item);\n      if (needLayer) {\n        content += \"}\";\n      }\n      if (item[2]) {\n        content += \"}\";\n      }\n      if (item[4]) {\n        content += \"}\";\n      }\n      return content;\n    }).join(\"\");\n  };\n\n  // import a list of modules into the list\n  list.i = function i(modules, media, dedupe, supports, layer) {\n    if (typeof modules === \"string\") {\n      modules = [[null, modules, undefined]];\n    }\n    var alreadyImportedModules = {};\n    if (dedupe) {\n      for (var k = 0; k < this.length; k++) {\n        var id = this[k][0];\n        if (id != null) {\n          alreadyImportedModules[id] = true;\n        }\n      }\n    }\n    for (var _k = 0; _k < modules.length; _k++) {\n      var item = [].concat(modules[_k]);\n      if (dedupe && alreadyImportedModules[item[0]]) {\n        continue;\n      }\n      if (typeof layer !== \"undefined\") {\n        if (typeof item[5] === \"undefined\") {\n          item[5] = layer;\n        } else {\n          item[1] = \"@layer\".concat(item[5].length > 0 ? \" \".concat(item[5]) : \"\", \" {\").concat(item[1], \"}\");\n          item[5] = layer;\n        }\n      }\n      if (media) {\n        if (!item[2]) {\n          item[2] = media;\n        } else {\n          item[1] = \"@media \".concat(item[2], \" {\").concat(item[1], \"}\");\n          item[2] = media;\n        }\n      }\n      if (supports) {\n        if (!item[4]) {\n          item[4] = \"\".concat(supports);\n        } else {\n          item[1] = \"@supports (\".concat(item[4], \") {\").concat(item[1], \"}\");\n          item[4] = supports;\n        }\n      }\n      list.push(item);\n    }\n  };\n  return list;\n};"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACAA,MAAM,CAACC,OAAO,GAAG,UAAUC,sBAAsB,EAAE;EACjD,IAAIC,IAAI,GAAG,EAAE;;EAEb;EACAA,IAAI,CAACC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IAClC,OAAO,IAAI,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;MAC9B,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,SAAS,GAAG,OAAOF,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW;MAC9C,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACXC,OAAO,IAAI,aAAa,CAACE,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;MACjD;MACA,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACXC,OAAO,IAAI,SAAS,CAACE,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;MAC5C;MACA,IAAIE,SAAS,EAAE;QACbD,OAAO,IAAI,QAAQ,CAACE,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,CAACD,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC;MACjF;MACAC,OAAO,IAAIL,sBAAsB,CAACI,IAAI,CAAC;MACvC,IAAIE,SAAS,EAAE;QACbD,OAAO,IAAI,GAAG;MAChB;MACA,IAAID,IAAI,CAAC,CAAC,CAAC,EAAE;QACXC,OAAO,IAAI,GAAG;MAChB;MACA,IAAID,IAAI,CAAC,CAAC,CAAC,EAAE;QACXC,OAAO,IAAI,GAAG;MAChB;MACA,OAAOA,OAAO;IAChB,CAAC,CAAC,CAACI,IAAI,CAAC,EAAE,CAAC;EACb,CAAC;;EAED;EACAR,IAAI,CAACS,CAAC,GAAG,SAASA,CAACA,CAACC,OAAO,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAE;IAC3D,IAAI,OAAOJ,OAAO,KAAK,QAAQ,EAAE;MAC/BA,OAAO,GAAG,CAAC,CAAC,IAAI,EAAEA,OAAO,EAAEK,SAAS,CAAC,CAAC;IACxC;IACA,IAAIC,sBAAsB,GAAG,CAAC,CAAC;IAC/B,IAAIJ,MAAM,EAAE;MACV,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACV,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAIC,EAAE,GAAG,IAAI,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,IAAIC,EAAE,IAAI,IAAI,EAAE;UACdF,sBAAsB,CAACE,EAAE,CAAC,GAAG,IAAI;QACnC;MACF;IACF;IACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGT,OAAO,CAACH,MAAM,EAAEY,EAAE,EAAE,EAAE;MAC1C,IAAIhB,IAAI,GAAG,EAAE,CAACG,MAAM,CAACI,OAAO,CAACS,EAAE,CAAC,CAAC;MACjC,IAAIP,MAAM,IAAII,sBAAsB,CAACb,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC7C;MACF;MACA,IAAI,OAAOW,KAAK,KAAK,WAAW,EAAE;QAChC,IAAI,OAAOX,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;UAClCA,IAAI,CAAC,CAAC,CAAC,GAAGW,KAAK;QACjB,CAAC,MAAM;UACLX,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,GAAG,CAAC,GAAG,GAAG,CAACD,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;UACnGA,IAAI,CAAC,CAAC,CAAC,GAAGW,KAAK;QACjB;MACF;MACA,IAAIH,KAAK,EAAE;QACT,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,EAAE;UACZA,IAAI,CAAC,CAAC,CAAC,GAAGQ,KAAK;QACjB,CAAC,MAAM;UACLR,IAAI,CAAC,CAAC,CAAC,GAAG,SAAS,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;UAC9DA,IAAI,CAAC,CAAC,CAAC,GAAGQ,KAAK;QACjB;MACF;MACA,IAAIE,QAAQ,EAAE;QACZ,IAAI,CAACV,IAAI,CAAC,CAAC,CAAC,EAAE;UACZA,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE,CAACG,MAAM,CAACO,QAAQ,CAAC;QAC/B,CAAC,MAAM;UACLV,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAACG,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;UACnEA,IAAI,CAAC,CAAC,CAAC,GAAGU,QAAQ;QACpB;MACF;MACAb,IAAI,CAACoB,IAAI,CAACjB,IAAI,CAAC;IACjB;EACF,CAAC;EACD,OAAOH,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}