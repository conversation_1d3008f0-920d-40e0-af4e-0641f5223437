{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { createPlatformFactory, NgModule } from '@angular/core';\nimport { platformBrowserDynamic } from './platform-browser-dynamic.mjs';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport '@angular/compiler';\nimport '@angular/platform-browser';\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformBrowserDynamic, 'browserDynamicTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n  static ɵfac = function BrowserDynamicTestingModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || BrowserDynamicTestingModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: BrowserDynamicTestingModule,\n    exports: [BrowserTestingModule]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [BrowserTestingModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserDynamicTestingModule, [{\n    type: NgModule,\n    args: [{\n      exports: [BrowserTestingModule]\n    }]\n  }], null, null);\n})();\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting };", "map": {"version": 3, "names": ["i0", "createPlatformFactory", "NgModule", "platformBrowserDynamic", "BrowserTestingModule", "platformBrowserDynamicTesting", "BrowserDynamicTestingModule", "ɵfac", "BrowserDynamicTestingModule_Factory", "__ngFactoryType__", "ɵmod", "ɵɵdefineNgModule", "type", "exports", "ɵinj", "ɵɵdefineInjector", "imports", "ngDevMode", "ɵsetClassMetadata", "args"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/@angular/platform-browser-dynamic/fesm2022/testing.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { createPlatformFactory, NgModule } from '@angular/core';\nimport { platformBrowserDynamic } from './platform-browser-dynamic.mjs';\nimport { BrowserTestingModule } from '@angular/platform-browser/testing';\nimport '@angular/compiler';\nimport '@angular/platform-browser';\n\n/**\n * @publicApi\n */\nconst platformBrowserDynamicTesting = createPlatformFactory(platformBrowserDynamic, 'browserDynamicTesting');\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\nclass BrowserDynamicTestingModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, exports: [BrowserTestingModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, imports: [BrowserTestingModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: BrowserDynamicTestingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [BrowserTestingModule],\n                }]\n        }] });\n\nexport { BrowserDynamicTestingModule, platformBrowserDynamicTesting };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,qBAAqB,EAAEC,QAAQ,QAAQ,eAAe;AAC/D,SAASC,sBAAsB,QAAQ,gCAAgC;AACvE,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,OAAO,mBAAmB;AAC1B,OAAO,2BAA2B;;AAElC;AACA;AACA;AACA,MAAMC,6BAA6B,GAAGJ,qBAAqB,CAACE,sBAAsB,EAAE,uBAAuB,CAAC;AAC5G;AACA;AACA;AACA;AACA;AACA,MAAMG,2BAA2B,CAAC;EAC9B,OAAOC,IAAI,YAAAC,oCAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFH,2BAA2B;EAAA;EAC/H,OAAOI,IAAI,kBAD+EV,EAAE,CAAAW,gBAAA;IAAAC,IAAA,EACSN,2BAA2B;IAAAO,OAAA,GAAYT,oBAAoB;EAAA;EAChK,OAAOU,IAAI,kBAF+Ed,EAAE,CAAAe,gBAAA;IAAAC,OAAA,GAEgDZ,oBAAoB;EAAA;AACpK;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAJ8FjB,EAAE,CAAAkB,iBAAA,CAIJZ,2BAA2B,EAAc,CAAC;IAC1HM,IAAI,EAAEV,QAAQ;IACdiB,IAAI,EAAE,CAAC;MACCN,OAAO,EAAE,CAACT,oBAAoB;IAClC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASE,2BAA2B,EAAED,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}