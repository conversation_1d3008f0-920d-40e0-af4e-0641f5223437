{"ast": null, "code": "/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n  static ɵfac = function PlatformNavigation_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PlatformNavigation)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PlatformNavigation,\n    factory: () => (() => window.navigation)(),\n    providedIn: 'platform'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PlatformNavigation, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'platform',\n      useFactory: () => window.navigation\n    }]\n  }], null, null);\n})();\nexport { PlatformNavigation };", "map": {"version": 3, "names": ["i0", "Injectable", "PlatformNavigation", "ɵfac", "PlatformNavigation_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "window", "navigation", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "useFactory"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/@angular/common/fesm2022/platform_navigation-B45Jeakb.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable } from '@angular/core';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\nclass PlatformNavigation {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, providedIn: 'platform', useFactory: () => window.navigation });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PlatformNavigation, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'platform', useFactory: () => window.navigation }]\n        }] });\n\nexport { PlatformNavigation };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,QAAQ,eAAe;;AAE1C;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,CAAC;EACrB,OAAOC,IAAI,YAAAC,2BAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFH,kBAAkB;EAAA;EACtH,OAAOI,KAAK,kBAD8EN,EAAE,CAAAO,kBAAA;IAAAC,KAAA,EACYN,kBAAkB;IAAAO,OAAA,EAAAA,CAAA,MAAsC,MAAMC,MAAM,CAACC,UAAU;IAAAC,UAAA,EAA/C;EAAU;AACtJ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAH8Fb,EAAE,CAAAc,iBAAA,CAGJZ,kBAAkB,EAAc,CAAC;IACjHa,IAAI,EAAEd,UAAU;IAChBe,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE,UAAU;MAAEK,UAAU,EAAEA,CAAA,KAAMP,MAAM,CAACC;IAAW,CAAC;EAC1E,CAAC,CAAC;AAAA;AAEV,SAAST,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}