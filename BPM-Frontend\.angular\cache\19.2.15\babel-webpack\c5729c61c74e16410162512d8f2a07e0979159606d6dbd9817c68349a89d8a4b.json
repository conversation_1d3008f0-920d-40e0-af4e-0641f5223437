{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { AsyncPipe, CommonModule, CurrencyPipe, DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe, DecimalPipe, FormStyle, FormatWidth, HashLocationStrategy, I18nPluralPipe, I18nSelectPipe, JsonPipe, KeyValuePipe, LowerCasePipe, NgClass, NgComponentOutlet, NgForOf as NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgLocaleLocalization, NgLocalization, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgS<PERSON>Default, NgTemplateOutlet, NumberFormatStyle, NumberSymbol, PercentPipe, Plural, SlicePipe, TitleCasePipe, TranslationWidth, UpperCasePipe, WeekDay, formatCurrency, formatDate, formatNumber, formatPercent, getCurrencySymbol, getLocaleCurrencyCode, getLocaleCurrencyName, getLocaleCurrencySymbol, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleDayNames, getLocaleDayPeriods, getLocaleDirection, getLocaleEraNames, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocaleFirstDayOfWeek, getLocaleId, getLocaleMonthNames, getLocaleNumberFormat, getLocaleNumberSymbol, getLocalePluralCase, getLocaleTimeFormat, getLocaleWeekEndRange, getNumberOfCurrencyDigits } from './common_module-Dx7dWex5.mjs';\nimport * as i0 from '@angular/core';\nimport { ɵregisterLocaleData as _registerLocaleData, Version, ɵɵdefineInjectable as __defineInjectable, inject, InjectionToken, ɵRuntimeError as _RuntimeError, ɵformatRuntimeError as _formatRuntimeError, PLATFORM_ID, Injectable, ɵIMAGE_CONFIG as _IMAGE_CONFIG, Renderer2, ElementRef, Injector, DestroyRef, ɵperformanceMarkFeature as _performanceMarkFeature, NgZone, ApplicationRef, booleanAttribute, numberAttribute, ChangeDetectorRef, ɵIMAGE_CONFIG_DEFAULTS as _IMAGE_CONFIG_DEFAULTS, ɵunwrapSafeValue as _unwrapSafeValue, Input, Directive } from '@angular/core';\nexport { ɵIMAGE_CONFIG as IMAGE_CONFIG } from '@angular/core';\nimport { isPlatformBrowser } from './xhr-BfNfxNDv.mjs';\nexport { XhrFactory, isPlatformServer, PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, parseCookieValue as ɵparseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\nexport { APP_BASE_HREF, BrowserPlatformLocation, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, DomAdapter as ɵDomAdapter, getDOM as ɵgetDOM, normalizeQueryParams as ɵnormalizeQueryParams, setRootDomAdapter as ɵsetRootDomAdapter } from './location-Dq4mJT-A.mjs';\nexport { PlatformNavigation as ɵPlatformNavigation } from './platform_navigation-B45Jeakb.mjs';\nimport 'rxjs';\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n/format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\nfunction registerLocaleData(data, localeId, extraData) {\n  return _registerLocaleData(data, localeId, extraData);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\nclass ViewportScroller {\n  // De-sugared tree-shakable injection\n  // See #23917\n  /** @nocollapse */\n  static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */__defineInjectable({\n    token: ViewportScroller,\n    providedIn: 'root',\n    factory: () => typeof ngServerMode !== 'undefined' && ngServerMode ? new NullViewportScroller() : new BrowserViewportScroller(inject(DOCUMENT), window)\n  });\n}\n/**\n * Manages the scroll position for a browser window.\n */\nclass BrowserViewportScroller {\n  document;\n  window;\n  offset = () => [0, 0];\n  constructor(document, window) {\n    this.document = document;\n    this.window = window;\n  }\n  /**\n   * Configures the top offset used when scrolling to an anchor.\n   * @param offset A position in screen coordinates (a tuple with x and y values)\n   * or a function that returns the top offset position.\n   *\n   */\n  setOffset(offset) {\n    if (Array.isArray(offset)) {\n      this.offset = () => offset;\n    } else {\n      this.offset = offset;\n    }\n  }\n  /**\n   * Retrieves the current scroll position.\n   * @returns The position in screen coordinates.\n   */\n  getScrollPosition() {\n    return [this.window.scrollX, this.window.scrollY];\n  }\n  /**\n   * Sets the scroll position.\n   * @param position The new position in screen coordinates.\n   */\n  scrollToPosition(position) {\n    this.window.scrollTo(position[0], position[1]);\n  }\n  /**\n   * Scrolls to an element and attempts to focus the element.\n   *\n   * Note that the function name here is misleading in that the target string may be an ID for a\n   * non-anchor element.\n   *\n   * @param target The ID of an element or name of the anchor.\n   *\n   * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n   * @see https://html.spec.whatwg.org/#scroll-to-fragid\n   */\n  scrollToAnchor(target) {\n    const elSelected = findAnchorFromDocument(this.document, target);\n    if (elSelected) {\n      this.scrollToElement(elSelected);\n      // After scrolling to the element, the spec dictates that we follow the focus steps for the\n      // target. Rather than following the robust steps, simply attempt focus.\n      //\n      // @see https://html.spec.whatwg.org/#get-the-focusable-area\n      // @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n      // @see https://html.spec.whatwg.org/#focusable-area\n      elSelected.focus();\n    }\n  }\n  /**\n   * Disables automatic scroll restoration provided by the browser.\n   */\n  setHistoryScrollRestoration(scrollRestoration) {\n    this.window.history.scrollRestoration = scrollRestoration;\n  }\n  /**\n   * Scrolls to an element using the native offset and the specified offset set on this scroller.\n   *\n   * The offset can be used when we know that there is a floating header and scrolling naively to an\n   * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n   */\n  scrollToElement(el) {\n    const rect = el.getBoundingClientRect();\n    const left = rect.left + this.window.pageXOffset;\n    const top = rect.top + this.window.pageYOffset;\n    const offset = this.offset();\n    this.window.scrollTo(left - offset[0], top - offset[1]);\n  }\n}\nfunction findAnchorFromDocument(document, target) {\n  const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n  if (documentResult) {\n    return documentResult;\n  }\n  // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n  // have to traverse the DOM manually and do the lookup through the shadow roots.\n  if (typeof document.createTreeWalker === 'function' && document.body && typeof document.body.attachShadow === 'function') {\n    const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n    let currentNode = treeWalker.currentNode;\n    while (currentNode) {\n      const shadowRoot = currentNode.shadowRoot;\n      if (shadowRoot) {\n        // Note that `ShadowRoot` doesn't support `getElementsByName`\n        // so we have to fall back to `querySelector`.\n        const result = shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n        if (result) {\n          return result;\n        }\n      }\n      currentNode = treeWalker.nextNode();\n    }\n  }\n  return null;\n}\n/**\n * Provides an empty implementation of the viewport scroller.\n */\nclass NullViewportScroller {\n  /**\n   * Empty implementation\n   */\n  setOffset(offset) {}\n  /**\n   * Empty implementation\n   */\n  getScrollPosition() {\n    return [0, 0];\n  }\n  /**\n   * Empty implementation\n   */\n  scrollToPosition(position) {}\n  /**\n   * Empty implementation\n   */\n  scrollToAnchor(anchor) {}\n  /**\n   * Empty implementation\n   */\n  setHistoryScrollRestoration(scrollRestoration) {}\n}\n\n/**\n * Value (out of 100) of the requested quality for placeholder images.\n */\nconst PLACEHOLDER_QUALITY = '20';\n\n// Converts a string that represents a URL into a URL class instance.\nfunction getUrl(src, win) {\n  // Don't use a base URL is the URL is absolute.\n  return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);\n}\n// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).\nfunction isAbsoluteUrl(src) {\n  return /^https?:\\/\\//.test(src);\n}\n// Given a URL, extract the hostname part.\n// If a URL is a relative one - the URL is returned as is.\nfunction extractHostname(url) {\n  return isAbsoluteUrl(url) ? new URL(url).hostname : url;\n}\nfunction isValidPath(path) {\n  const isString = typeof path === 'string';\n  if (!isString || path.trim() === '') {\n    return false;\n  }\n  // Calling new URL() will throw if the path string is malformed\n  try {\n    const url = new URL(path);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction normalizePath(path) {\n  return path.endsWith('/') ? path.slice(0, -1) : path;\n}\nfunction normalizeSrc(src) {\n  return src.startsWith('/') ? src.slice(1) : src;\n}\n\n/**\n * Noop image loader that does no transformation to the original src and just returns it as is.\n * This loader is used as a default one if more specific logic is not provided in an app config.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n */\nconst noopImageLoader = config => config.src;\n/**\n * Injection token that configures the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nconst IMAGE_LOADER = new InjectionToken(ngDevMode ? 'ImageLoader' : '', {\n  providedIn: 'root',\n  factory: () => noopImageLoader\n});\n/**\n * Internal helper function that makes it easier to introduce custom image loaders for the\n * `NgOptimizedImage` directive. It is enough to specify a URL builder function to obtain full DI\n * configuration for a given loader: a DI token corresponding to the actual loader function, plus DI\n * tokens managing preconnect check functionality.\n * @param buildUrlFn a function returning a full URL based on loader's configuration\n * @param exampleUrls example of full URLs for a given loader (used in error messages)\n * @returns a set of DI providers corresponding to the configured image loader\n */\nfunction createImageLoader(buildUrlFn, exampleUrls) {\n  return function provideImageLoader(path) {\n    if (!isValidPath(path)) {\n      throwInvalidPathError(path, exampleUrls || []);\n    }\n    // The trailing / is stripped (if provided) to make URL construction (concatenation) easier in\n    // the individual loader functions.\n    path = normalizePath(path);\n    const loaderFn = config => {\n      if (isAbsoluteUrl(config.src)) {\n        // Image loader functions expect an image file name (e.g. `my-image.png`)\n        // or a relative path + a file name (e.g. `/a/b/c/my-image.png`) as an input,\n        // so the final absolute URL can be constructed.\n        // When an absolute URL is provided instead - the loader can not\n        // build a final URL, thus the error is thrown to indicate that.\n        throwUnexpectedAbsoluteUrlError(path, config.src);\n      }\n      return buildUrlFn(path, {\n        ...config,\n        src: normalizeSrc(config.src)\n      });\n    };\n    const providers = [{\n      provide: IMAGE_LOADER,\n      useValue: loaderFn\n    }];\n    return providers;\n  };\n}\nfunction throwInvalidPathError(path, exampleUrls) {\n  throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected an invalid path (\\`${path}\\`). ` + `To fix this, supply a path using one of the following formats: ${exampleUrls.join(' or ')}`);\n}\nfunction throwUnexpectedAbsoluteUrlError(path, url) {\n  throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected a \\`<img>\\` tag with an invalid \\`ngSrc\\` attribute: ${url}. ` + `This image loader expects \\`ngSrc\\` to be a relative URL - ` + `however the provided value is an absolute URL. ` + `To fix this, provide \\`ngSrc\\` as a path relative to the base URL ` + `configured for this loader (\\`${path}\\`).`);\n}\n\n/**\n * Function that generates an ImageLoader for [Cloudflare Image\n * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular\n * provider. Note: Cloudflare has multiple image products - this provider is specifically for\n * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.\n *\n * @param path Your domain name, e.g. https://mysite.com\n * @returns Provider that provides an ImageLoader function\n *\n * @publicApi\n */\nconst provideCloudflareLoader = createImageLoader(createCloudflareUrl, ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined);\nfunction createCloudflareUrl(path, config) {\n  let params = `format=auto`;\n  if (config.width) {\n    params += `,width=${config.width}`;\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params += `,quality=${PLACEHOLDER_QUALITY}`;\n  }\n  // Cloudflare image URLs format:\n  // https://developers.cloudflare.com/images/image-resizing/url-format/\n  return `${path}/cdn-cgi/image/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for Cloudinary.\n */\nconst cloudinaryLoaderInfo = {\n  name: 'Cloudinary',\n  testUrl: isCloudinaryUrl\n};\nconst CLOUDINARY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.cloudinary\\.com\\/.+/;\n/**\n * Tests whether a URL is from Cloudinary CDN.\n */\nfunction isCloudinaryUrl(url) {\n  return CLOUDINARY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Cloudinary and turns it into an Angular provider.\n *\n * @param path Base URL of your Cloudinary images\n * This URL should match one of the following formats:\n * https://res.cloudinary.com/mysite\n * https://mysite.cloudinary.com\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the Cloudinary loader.\n *\n * @publicApi\n */\nconst provideCloudinaryLoader = createImageLoader(createCloudinaryUrl, ngDevMode ? ['https://res.cloudinary.com/mysite', 'https://mysite.cloudinary.com', 'https://subdomain.mysite.com'] : undefined);\nfunction createCloudinaryUrl(path, config) {\n  // Cloudinary image URLformat:\n  // https://cloudinary.com/documentation/image_transformations#transformation_url_structure\n  // Example of a Cloudinary image URL:\n  // https://res.cloudinary.com/mysite/image/upload/c_scale,f_auto,q_auto,w_600/marketing/tile-topics-m.png\n  // For a placeholder image, we use the lowest image setting available to reduce the load time\n  // else we use the auto size\n  const quality = config.isPlaceholder ? 'q_auto:low' : 'q_auto';\n  let params = `f_auto,${quality}`;\n  if (config.width) {\n    params += `,w_${config.width}`;\n  }\n  if (config.loaderParams?.['rounded']) {\n    params += `,r_max`;\n  }\n  return `${path}/image/upload/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for ImageKit.\n */\nconst imageKitLoaderInfo = {\n  name: 'ImageKit',\n  testUrl: isImageKitUrl\n};\nconst IMAGE_KIT_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imagekit\\.io\\/.+/;\n/**\n * Tests whether a URL is from ImageKit CDN.\n */\nfunction isImageKitUrl(url) {\n  return IMAGE_KIT_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for ImageKit and turns it into an Angular provider.\n *\n * @param path Base URL of your ImageKit images\n * This URL should match one of the following formats:\n * https://ik.imagekit.io/myaccount\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the ImageKit loader.\n *\n * @publicApi\n */\nconst provideImageKitLoader = createImageLoader(createImagekitUrl, ngDevMode ? ['https://ik.imagekit.io/mysite', 'https://subdomain.mysite.com'] : undefined);\nfunction createImagekitUrl(path, config) {\n  // Example of an ImageKit image URL:\n  // https://ik.imagekit.io/demo/tr:w-300,h-300/medium_cafe_B1iTdD0C.jpg\n  const {\n    src,\n    width\n  } = config;\n  const params = [];\n  if (width) {\n    params.push(`w-${width}`);\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    params.push(`q-${PLACEHOLDER_QUALITY}`);\n  }\n  const urlSegments = params.length ? [path, `tr:${params.join(',')}`, src] : [path, src];\n  const url = new URL(urlSegments.join('/'));\n  return url.href;\n}\n\n/**\n * Name and URL tester for Imgix.\n */\nconst imgixLoaderInfo = {\n  name: 'Imgix',\n  testUrl: isImgixUrl\n};\nconst IMGIX_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imgix\\.net\\/.+/;\n/**\n * Tests whether a URL is from Imgix CDN.\n */\nfunction isImgixUrl(url) {\n  return IMGIX_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.\n *\n * @param path path to the desired Imgix origin,\n * e.g. https://somepath.imgix.net or https://images.mysite.com\n * @returns Set of providers to configure the Imgix loader.\n *\n * @publicApi\n */\nconst provideImgixLoader = createImageLoader(createImgixUrl, ngDevMode ? ['https://somepath.imgix.net/'] : undefined);\nfunction createImgixUrl(path, config) {\n  const url = new URL(`${path}/${config.src}`);\n  // This setting ensures the smallest allowable format is set.\n  url.searchParams.set('auto', 'format');\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n  // When requesting a placeholder image we ask a low quality image to reduce the load time.\n  if (config.isPlaceholder) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  return url.href;\n}\n\n/**\n * Name and URL tester for Netlify.\n */\nconst netlifyLoaderInfo = {\n  name: 'Netlify',\n  testUrl: isNetlifyUrl\n};\nconst NETLIFY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.netlify\\.app\\/.+/;\n/**\n * Tests whether a URL is from a Netlify site. This won't catch sites with a custom domain,\n * but it's a good start for sites in development. This is only used to warn users who haven't\n * configured an image loader.\n */\nfunction isNetlifyUrl(url) {\n  return NETLIFY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Netlify and turns it into an Angular provider.\n *\n * @param path optional URL of the desired Netlify site. Defaults to the current site.\n * @returns Set of providers to configure the Netlify loader.\n *\n * @publicApi\n */\nfunction provideNetlifyLoader(path) {\n  if (path && !isValidPath(path)) {\n    throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode && `Image loader has detected an invalid path (\\`${path}\\`). ` + `To fix this, supply either the full URL to the Netlify site, or leave it empty to use the current site.`);\n  }\n  if (path) {\n    const url = new URL(path);\n    path = url.origin;\n  }\n  const loaderFn = config => {\n    return createNetlifyUrl(config, path);\n  };\n  const providers = [{\n    provide: IMAGE_LOADER,\n    useValue: loaderFn\n  }];\n  return providers;\n}\nconst validParams = new Map([['height', 'h'], ['fit', 'fit'], ['quality', 'q'], ['q', 'q'], ['position', 'position']]);\nfunction createNetlifyUrl(config, path) {\n  // Note: `path` can be undefined, in which case we use a fake one to construct a `URL` instance.\n  const url = new URL(path ?? 'https://a/');\n  url.pathname = '/.netlify/images';\n  if (!isAbsoluteUrl(config.src) && !config.src.startsWith('/')) {\n    config.src = '/' + config.src;\n  }\n  url.searchParams.set('url', config.src);\n  if (config.width) {\n    url.searchParams.set('w', config.width.toString());\n  }\n  // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n  // If the quality is specified in the loader config - always use provided value.\n  const configQuality = config.loaderParams?.['quality'] ?? config.loaderParams?.['q'];\n  if (config.isPlaceholder && !configQuality) {\n    url.searchParams.set('q', PLACEHOLDER_QUALITY);\n  }\n  for (const [param, value] of Object.entries(config.loaderParams ?? {})) {\n    if (validParams.has(param)) {\n      url.searchParams.set(validParams.get(param), value.toString());\n    } else {\n      if (ngDevMode) {\n        console.warn(_formatRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, `The Netlify image loader has detected an \\`<img>\\` tag with the unsupported attribute \"\\`${param}\\`\".`));\n      }\n    }\n  }\n  // The \"a\" hostname is used for relative URLs, so we can remove it from the final URL.\n  return url.hostname === 'a' ? url.href.replace(url.origin, '') : url.href;\n}\n\n// Assembles directive details string, useful for error messages.\nfunction imgDirectiveDetails(ngSrc, includeNgSrc = true) {\n  const ngSrcInfo = includeNgSrc ? `(activated on an <img> element with the \\`ngSrc=\"${ngSrc}\"\\`) ` : '';\n  return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;\n}\n\n/**\n * Asserts that the application is in development mode. Throws an error if the application is in\n * production mode. This assert can be used to make sure that there is no dev-mode code invoked in\n * the prod mode accidentally.\n */\nfunction assertDevMode(checkName) {\n  if (!ngDevMode) {\n    throw new _RuntimeError(2958 /* RuntimeErrorCode.UNEXPECTED_DEV_MODE_CHECK_IN_PROD_MODE */, `Unexpected invocation of the ${checkName} in the prod mode. ` + `Please make sure that the prod mode is enabled for production builds.`);\n  }\n}\n\n/**\n * Observer that detects whether an image with `NgOptimizedImage`\n * is treated as a Largest Contentful Paint (LCP) element. If so,\n * asserts that the image has the `priority` attribute.\n *\n * Note: this is a dev-mode only class and it does not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n *\n * Based on https://web.dev/lcp/#measure-lcp-in-javascript.\n */\nclass LCPImageObserver {\n  // Map of full image URLs -> original `ngSrc` values.\n  images = new Map();\n  window = null;\n  observer = null;\n  constructor() {\n    const isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n    assertDevMode('LCP checker');\n    const win = inject(DOCUMENT).defaultView;\n    if (isBrowser && typeof PerformanceObserver !== 'undefined') {\n      this.window = win;\n      this.observer = this.initPerformanceObserver();\n    }\n  }\n  /**\n   * Inits PerformanceObserver and subscribes to LCP events.\n   * Based on https://web.dev/lcp/#measure-lcp-in-javascript\n   */\n  initPerformanceObserver() {\n    const observer = new PerformanceObserver(entryList => {\n      const entries = entryList.getEntries();\n      if (entries.length === 0) return;\n      // We use the latest entry produced by the `PerformanceObserver` as the best\n      // signal on which element is actually an LCP one. As an example, the first image to load on\n      // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n      // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n      const lcpElement = entries[entries.length - 1];\n      // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n      // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n      const imgSrc = lcpElement.element?.src ?? '';\n      // Exclude `data:` and `blob:` URLs, since they are not supported by the directive.\n      if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:')) return;\n      const img = this.images.get(imgSrc);\n      if (!img) return;\n      if (!img.priority && !img.alreadyWarnedPriority) {\n        img.alreadyWarnedPriority = true;\n        logMissingPriorityError(imgSrc);\n      }\n      if (img.modified && !img.alreadyWarnedModified) {\n        img.alreadyWarnedModified = true;\n        logModifiedWarning(imgSrc);\n      }\n    });\n    observer.observe({\n      type: 'largest-contentful-paint',\n      buffered: true\n    });\n    return observer;\n  }\n  registerImage(rewrittenSrc, originalNgSrc, isPriority) {\n    if (!this.observer) return;\n    const newObservedImageState = {\n      priority: isPriority,\n      modified: false,\n      alreadyWarnedModified: false,\n      alreadyWarnedPriority: false\n    };\n    this.images.set(getUrl(rewrittenSrc, this.window).href, newObservedImageState);\n  }\n  unregisterImage(rewrittenSrc) {\n    if (!this.observer) return;\n    this.images.delete(getUrl(rewrittenSrc, this.window).href);\n  }\n  updateImage(originalSrc, newSrc) {\n    if (!this.observer) return;\n    const originalUrl = getUrl(originalSrc, this.window).href;\n    const img = this.images.get(originalUrl);\n    if (img) {\n      img.modified = true;\n      this.images.set(getUrl(newSrc, this.window).href, img);\n      this.images.delete(originalUrl);\n    }\n  }\n  ngOnDestroy() {\n    if (!this.observer) return;\n    this.observer.disconnect();\n    this.images.clear();\n  }\n  static ɵfac = function LCPImageObserver_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LCPImageObserver)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: LCPImageObserver,\n    factory: LCPImageObserver.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LCPImageObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\nfunction logMissingPriorityError(ngSrc) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.error(_formatRuntimeError(2955 /* RuntimeErrorCode.LCP_IMG_MISSING_PRIORITY */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` + `element but was not marked \"priority\". This image should be marked ` + `\"priority\" in order to prioritize its loading. ` + `To fix this, add the \"priority\" attribute.`));\n}\nfunction logModifiedWarning(ngSrc) {\n  const directiveDetails = imgDirectiveDetails(ngSrc);\n  console.warn(_formatRuntimeError(2964 /* RuntimeErrorCode.LCP_IMG_NGSRC_MODIFIED */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` + `element and has had its \"ngSrc\" attribute modified. This can cause ` + `slower loading performance. It is recommended not to modify the \"ngSrc\" ` + `property on any image which could be the LCP element.`));\n}\n\n// Set of origins that are always excluded from the preconnect checks.\nconst INTERNAL_PRECONNECT_CHECK_BLOCKLIST = new Set(['localhost', '127.0.0.1', '0.0.0.0']);\n/**\n * Injection token to configure which origins should be excluded\n * from the preconnect checks. It can either be a single string or an array of strings\n * to represent a group of origins, for example:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST, useValue: 'https://your-domain.com'}\n * ```\n *\n * or:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST,\n *   useValue: ['https://your-domain-1.com', 'https://your-domain-2.com']}\n * ```\n *\n * @publicApi\n */\nconst PRECONNECT_CHECK_BLOCKLIST = new InjectionToken(ngDevMode ? 'PRECONNECT_CHECK_BLOCKLIST' : '');\n/**\n * Contains the logic to detect whether an image, marked with the \"priority\" attribute\n * has a corresponding `<link rel=\"preconnect\">` tag in the `document.head`.\n *\n * Note: this is a dev-mode only class, which should not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n */\nclass PreconnectLinkChecker {\n  document = inject(DOCUMENT);\n  /**\n   * Set of <link rel=\"preconnect\"> tags found on this page.\n   * The `null` value indicates that there was no DOM query operation performed.\n   */\n  preconnectLinks = null;\n  /*\n   * Keep track of all already seen origin URLs to avoid repeating the same check.\n   */\n  alreadySeen = new Set();\n  window = this.document.defaultView;\n  blocklist = new Set(INTERNAL_PRECONNECT_CHECK_BLOCKLIST);\n  constructor() {\n    assertDevMode('preconnect link checker');\n    const blocklist = inject(PRECONNECT_CHECK_BLOCKLIST, {\n      optional: true\n    });\n    if (blocklist) {\n      this.populateBlocklist(blocklist);\n    }\n  }\n  populateBlocklist(origins) {\n    if (Array.isArray(origins)) {\n      deepForEach(origins, origin => {\n        this.blocklist.add(extractHostname(origin));\n      });\n    } else {\n      this.blocklist.add(extractHostname(origins));\n    }\n  }\n  /**\n   * Checks that a preconnect resource hint exists in the head for the\n   * given src.\n   *\n   * @param rewrittenSrc src formatted with loader\n   * @param originalNgSrc ngSrc value\n   */\n  assertPreconnect(rewrittenSrc, originalNgSrc) {\n    if (typeof ngServerMode !== 'undefined' && ngServerMode) return;\n    const imgUrl = getUrl(rewrittenSrc, this.window);\n    if (this.blocklist.has(imgUrl.hostname) || this.alreadySeen.has(imgUrl.origin)) return;\n    // Register this origin as seen, so we don't check it again later.\n    this.alreadySeen.add(imgUrl.origin);\n    // Note: we query for preconnect links only *once* and cache the results\n    // for the entire lifespan of an application, since it's unlikely that the\n    // list would change frequently. This allows to make sure there are no\n    // performance implications of making extra DOM lookups for each image.\n    this.preconnectLinks ??= this.queryPreconnectLinks();\n    if (!this.preconnectLinks.has(imgUrl.origin)) {\n      console.warn(_formatRuntimeError(2956 /* RuntimeErrorCode.PRIORITY_IMG_MISSING_PRECONNECT_TAG */, `${imgDirectiveDetails(originalNgSrc)} there is no preconnect tag present for this ` + `image. Preconnecting to the origin(s) that serve priority images ensures that these ` + `images are delivered as soon as possible. To fix this, please add the following ` + `element into the <head> of the document:\\n` + `  <link rel=\"preconnect\" href=\"${imgUrl.origin}\">`));\n    }\n  }\n  queryPreconnectLinks() {\n    const preconnectUrls = new Set();\n    const links = this.document.querySelectorAll('link[rel=preconnect]');\n    for (const link of links) {\n      const url = getUrl(link.href, this.window);\n      preconnectUrls.add(url.origin);\n    }\n    return preconnectUrls;\n  }\n  ngOnDestroy() {\n    this.preconnectLinks?.clear();\n    this.alreadySeen.clear();\n  }\n  static ɵfac = function PreconnectLinkChecker_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PreconnectLinkChecker)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PreconnectLinkChecker,\n    factory: PreconnectLinkChecker.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreconnectLinkChecker, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n/**\n * Invokes a callback for each element in the array. Also invokes a callback\n * recursively for each nested array.\n */\nfunction deepForEach(input, fn) {\n  for (let value of input) {\n    Array.isArray(value) ? deepForEach(value, fn) : fn(value);\n  }\n}\n\n/**\n * In SSR scenarios, a preload `<link>` element is generated for priority images.\n * Having a large number of preload tags may negatively affect the performance,\n * so we warn developers (by throwing an error) if the number of preloaded images\n * is above a certain threshold. This const specifies this threshold.\n */\nconst DEFAULT_PRELOADED_IMAGES_LIMIT = 5;\n/**\n * Helps to keep track of priority images that already have a corresponding\n * preload tag (to avoid generating multiple preload tags with the same URL).\n *\n * This Set tracks the original src passed into the `ngSrc` input not the src after it has been\n * run through the specified `IMAGE_LOADER`.\n */\nconst PRELOADED_IMAGES = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'NG_OPTIMIZED_PRELOADED_IMAGES' : '', {\n  providedIn: 'root',\n  factory: () => new Set()\n});\n\n/**\n * @description Contains the logic needed to track and add preload link tags to the `<head>` tag. It\n * will also track what images have already had preload link tags added so as to not duplicate link\n * tags.\n *\n * In dev mode this service will validate that the number of preloaded images does not exceed the\n * configured default preloaded images limit: {@link DEFAULT_PRELOADED_IMAGES_LIMIT}.\n */\nclass PreloadLinkCreator {\n  preloadedImages = inject(PRELOADED_IMAGES);\n  document = inject(DOCUMENT);\n  errorShown = false;\n  /**\n   * @description Add a preload `<link>` to the `<head>` of the `index.html` that is served from the\n   * server while using Angular Universal and SSR to kick off image loads for high priority images.\n   *\n   * The `sizes` (passed in from the user) and `srcset` (parsed and formatted from `ngSrcset`)\n   * properties used to set the corresponding attributes, `imagesizes` and `imagesrcset`\n   * respectively, on the preload `<link>` tag so that the correctly sized image is preloaded from\n   * the CDN.\n   *\n   * {@link https://web.dev/preload-responsive-images/#imagesrcset-and-imagesizes}\n   *\n   * @param renderer The `Renderer2` passed in from the directive\n   * @param src The original src of the image that is set on the `ngSrc` input.\n   * @param srcset The parsed and formatted srcset created from the `ngSrcset` input\n   * @param sizes The value of the `sizes` attribute passed in to the `<img>` tag\n   */\n  createPreloadLinkTag(renderer, src, srcset, sizes) {\n    if (ngDevMode && !this.errorShown && this.preloadedImages.size >= DEFAULT_PRELOADED_IMAGES_LIMIT) {\n      this.errorShown = true;\n      console.warn(_formatRuntimeError(2961 /* RuntimeErrorCode.TOO_MANY_PRELOADED_IMAGES */, `The \\`NgOptimizedImage\\` directive has detected that more than ` + `${DEFAULT_PRELOADED_IMAGES_LIMIT} images were marked as priority. ` + `This might negatively affect an overall performance of the page. ` + `To fix this, remove the \"priority\" attribute from images with less priority.`));\n    }\n    if (this.preloadedImages.has(src)) {\n      return;\n    }\n    this.preloadedImages.add(src);\n    const preload = renderer.createElement('link');\n    renderer.setAttribute(preload, 'as', 'image');\n    renderer.setAttribute(preload, 'href', src);\n    renderer.setAttribute(preload, 'rel', 'preload');\n    renderer.setAttribute(preload, 'fetchpriority', 'high');\n    if (sizes) {\n      renderer.setAttribute(preload, 'imageSizes', sizes);\n    }\n    if (srcset) {\n      renderer.setAttribute(preload, 'imageSrcset', srcset);\n    }\n    renderer.appendChild(this.document.head, preload);\n  }\n  static ɵfac = function PreloadLinkCreator_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PreloadLinkCreator)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: PreloadLinkCreator,\n    factory: PreloadLinkCreator.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(PreloadLinkCreator, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n\n/**\n * When a Base64-encoded image is passed as an input to the `NgOptimizedImage` directive,\n * an error is thrown. The image content (as a string) might be very long, thus making\n * it hard to read an error message if the entire string is included. This const defines\n * the number of characters that should be included into the error message. The rest\n * of the content is truncated.\n */\nconst BASE64_IMG_MAX_LENGTH_IN_ERROR = 50;\n/**\n * RegExpr to determine whether a src in a srcset is using width descriptors.\n * Should match something like: \"100w, 200w\".\n */\nconst VALID_WIDTH_DESCRIPTOR_SRCSET = /^((\\s*\\d+w\\s*(,|$)){1,})$/;\n/**\n * RegExpr to determine whether a src in a srcset is using density descriptors.\n * Should match something like: \"1x, 2x, 50x\". Also supports decimals like \"1.5x, 1.50x\".\n */\nconst VALID_DENSITY_DESCRIPTOR_SRCSET = /^((\\s*\\d+(\\.\\d+)?x\\s*(,|$)){1,})$/;\n/**\n * Srcset values with a density descriptor higher than this value will actively\n * throw an error. Such densities are not permitted as they cause image sizes\n * to be unreasonably large and slow down LCP.\n */\nconst ABSOLUTE_SRCSET_DENSITY_CAP = 3;\n/**\n * Used only in error message text to communicate best practices, as we will\n * only throw based on the slightly more conservative ABSOLUTE_SRCSET_DENSITY_CAP.\n */\nconst RECOMMENDED_SRCSET_DENSITY_CAP = 2;\n/**\n * Used in generating automatic density-based srcsets\n */\nconst DENSITY_SRCSET_MULTIPLIERS = [1, 2];\n/**\n * Used to determine which breakpoints to use on full-width images\n */\nconst VIEWPORT_BREAKPOINT_CUTOFF = 640;\n/**\n * Used to determine whether two aspect ratios are similar in value.\n */\nconst ASPECT_RATIO_TOLERANCE = 0.1;\n/**\n * Used to determine whether the image has been requested at an overly\n * large size compared to the actual rendered image size (after taking\n * into account a typical device pixel ratio). In pixels.\n */\nconst OVERSIZED_IMAGE_TOLERANCE = 1000;\n/**\n * Used to limit automatic srcset generation of very large sources for\n * fixed-size images. In pixels.\n */\nconst FIXED_SRCSET_WIDTH_LIMIT = 1920;\nconst FIXED_SRCSET_HEIGHT_LIMIT = 1080;\n/**\n * Default blur radius of the CSS filter used on placeholder images, in pixels\n */\nconst PLACEHOLDER_BLUR_AMOUNT = 15;\n/**\n * Placeholder dimension (height or width) limit in pixels. Angular produces a warning\n * when this limit is crossed.\n */\nconst PLACEHOLDER_DIMENSION_LIMIT = 1000;\n/**\n * Used to warn or error when the user provides an overly large dataURL for the placeholder\n * attribute.\n * Character count of Base64 images is 1 character per byte, and base64 encoding is approximately\n * 33% larger than base images, so 4000 characters is around 3KB on disk and 10000 characters is\n * around 7.7KB. Experimentally, 4000 characters is about 20x20px in PNG or medium-quality JPEG\n * format, and 10,000 is around 50x50px, but there's quite a bit of variation depending on how the\n * image is saved.\n */\nconst DATA_URL_WARN_LIMIT = 4000;\nconst DATA_URL_ERROR_LIMIT = 10000;\n/** Info about built-in loaders we can test for. */\nconst BUILT_IN_LOADERS = [imgixLoaderInfo, imageKitLoaderInfo, cloudinaryLoaderInfo, netlifyLoaderInfo];\n/**\n * Threshold for the PRIORITY_TRUE_COUNT\n */\nconst PRIORITY_COUNT_THRESHOLD = 10;\n/**\n * This count is used to log a devMode warning\n * when the count of directive instances with priority=true\n * exceeds the threshold PRIORITY_COUNT_THRESHOLD\n */\nlet IMGS_WITH_PRIORITY_ATTR_COUNT = 0;\n/**\n * Directive that improves image loading performance by enforcing best practices.\n *\n * `NgOptimizedImage` ensures that the loading of the Largest Contentful Paint (LCP) image is\n * prioritized by:\n * - Automatically setting the `fetchpriority` attribute on the `<img>` tag\n * - Lazy loading non-priority images by default\n * - Automatically generating a preconnect link tag in the document head\n *\n * In addition, the directive:\n * - Generates appropriate asset URLs if a corresponding `ImageLoader` function is provided\n * - Automatically generates a srcset\n * - Requires that `width` and `height` are set\n * - Warns if `width` or `height` have been set incorrectly\n * - Warns if the image will be visually distorted when rendered\n *\n * @usageNotes\n * The `NgOptimizedImage` directive is marked as [standalone](guide/components/importing) and can\n * be imported directly.\n *\n * Follow the steps below to enable and use the directive:\n * 1. Import it into the necessary NgModule or a standalone Component.\n * 2. Optionally provide an `ImageLoader` if you use an image hosting service.\n * 3. Update the necessary `<img>` tags in templates and replace `src` attributes with `ngSrc`.\n * Using a `ngSrc` allows the directive to control when the `src` gets set, which triggers an image\n * download.\n *\n * Step 1: import the `NgOptimizedImage` directive.\n *\n * ```ts\n * import { NgOptimizedImage } from '@angular/common';\n *\n * // Include it into the necessary NgModule\n * @NgModule({\n *   imports: [NgOptimizedImage],\n * })\n * class AppModule {}\n *\n * // ... or a standalone Component\n * @Component({\n *   imports: [NgOptimizedImage],\n * })\n * class MyStandaloneComponent {}\n * ```\n *\n * Step 2: configure a loader.\n *\n * To use the **default loader**: no additional code changes are necessary. The URL returned by the\n * generic loader will always match the value of \"src\". In other words, this loader applies no\n * transformations to the resource URL and the value of the `ngSrc` attribute will be used as is.\n *\n * To use an existing loader for a **third-party image service**: add the provider factory for your\n * chosen service to the `providers` array. In the example below, the Imgix loader is used:\n *\n * ```ts\n * import {provideImgixLoader} from '@angular/common';\n *\n * // Call the function and add the result to the `providers` array:\n * providers: [\n *   provideImgixLoader(\"https://my.base.url/\"),\n * ],\n * ```\n *\n * The `NgOptimizedImage` directive provides the following functions:\n * - `provideCloudflareLoader`\n * - `provideCloudinaryLoader`\n * - `provideImageKitLoader`\n * - `provideImgixLoader`\n *\n * If you use a different image provider, you can create a custom loader function as described\n * below.\n *\n * To use a **custom loader**: provide your loader function as a value for the `IMAGE_LOADER` DI\n * token.\n *\n * ```ts\n * import {IMAGE_LOADER, ImageLoaderConfig} from '@angular/common';\n *\n * // Configure the loader using the `IMAGE_LOADER` token.\n * providers: [\n *   {\n *      provide: IMAGE_LOADER,\n *      useValue: (config: ImageLoaderConfig) => {\n *        return `https://example.com/${config.src}-${config.width}.jpg`;\n *      }\n *   },\n * ],\n * ```\n *\n * Step 3: update `<img>` tags in templates to use `ngSrc` instead of `src`.\n *\n * ```html\n * <img ngSrc=\"logo.png\" width=\"200\" height=\"100\">\n * ```\n *\n * @publicApi\n */\nclass NgOptimizedImage {\n  imageLoader = inject(IMAGE_LOADER);\n  config = processConfig(inject(_IMAGE_CONFIG));\n  renderer = inject(Renderer2);\n  imgElement = inject(ElementRef).nativeElement;\n  injector = inject(Injector);\n  // An LCP image observer should be injected only in development mode.\n  // Do not assign it to `null` to avoid having a redundant property in the production bundle.\n  lcpObserver;\n  /**\n   * Calculate the rewritten `src` once and store it.\n   * This is needed to avoid repetitive calculations and make sure the directive cleanup in the\n   * `ngOnDestroy` does not rely on the `IMAGE_LOADER` logic (which in turn can rely on some other\n   * instance that might be already destroyed).\n   */\n  _renderedSrc = null;\n  /**\n   * Name of the source image.\n   * Image name will be processed by the image loader and the final URL will be applied as the `src`\n   * property of the image.\n   */\n  ngSrc;\n  /**\n   * A comma separated list of width or density descriptors.\n   * The image name will be taken from `ngSrc` and combined with the list of width or density\n   * descriptors to generate the final `srcset` property of the image.\n   *\n   * Example:\n   * ```html\n   * <img ngSrc=\"hello.jpg\" ngSrcset=\"100w, 200w\" />  =>\n   * <img src=\"path/hello.jpg\" srcset=\"path/hello.jpg?w=100 100w, path/hello.jpg?w=200 200w\" />\n   * ```\n   */\n  ngSrcset;\n  /**\n   * The base `sizes` attribute passed through to the `<img>` element.\n   * Providing sizes causes the image to create an automatic responsive srcset.\n   */\n  sizes;\n  /**\n   * For responsive images: the intrinsic width of the image in pixels.\n   * For fixed size images: the desired rendered width of the image in pixels.\n   */\n  width;\n  /**\n   * For responsive images: the intrinsic height of the image in pixels.\n   * For fixed size images: the desired rendered height of the image in pixels.\n   */\n  height;\n  /**\n   * The desired loading behavior (lazy, eager, or auto). Defaults to `lazy`,\n   * which is recommended for most images.\n   *\n   * Warning: Setting images as loading=\"eager\" or loading=\"auto\" marks them\n   * as non-priority images and can hurt loading performance. For images which\n   * may be the LCP element, use the `priority` attribute instead of `loading`.\n   */\n  loading;\n  /**\n   * Indicates whether this image should have a high priority.\n   */\n  priority = false;\n  /**\n   * Data to pass through to custom loaders.\n   */\n  loaderParams;\n  /**\n   * Disables automatic srcset generation for this image.\n   */\n  disableOptimizedSrcset = false;\n  /**\n   * Sets the image to \"fill mode\", which eliminates the height/width requirement and adds\n   * styles such that the image fills its containing element.\n   */\n  fill = false;\n  /**\n   * A URL or data URL for an image to be used as a placeholder while this image loads.\n   */\n  placeholder;\n  /**\n   * Configuration object for placeholder settings. Options:\n   *   * blur: Setting this to false disables the automatic CSS blur.\n   */\n  placeholderConfig;\n  /**\n   * Value of the `src` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `src` is not set in conflict\n   * with `ngSrc` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  src;\n  /**\n   * Value of the `srcset` attribute if set on the host `<img>` element.\n   * This input is exclusively read to assert that `srcset` is not set in conflict\n   * with `ngSrcset` and that images don't start to load until a lazy loading strategy is set.\n   * @internal\n   */\n  srcset;\n  constructor() {\n    if (ngDevMode) {\n      this.lcpObserver = this.injector.get(LCPImageObserver);\n      // Using `DestroyRef` to avoid having an empty `ngOnDestroy` method since this\n      // is only run in development mode.\n      const destroyRef = inject(DestroyRef);\n      destroyRef.onDestroy(() => {\n        if (!this.priority && this._renderedSrc !== null) {\n          this.lcpObserver.unregisterImage(this._renderedSrc);\n        }\n      });\n    }\n  }\n  /** @docs-private */\n  ngOnInit() {\n    _performanceMarkFeature('NgOptimizedImage');\n    if (ngDevMode) {\n      const ngZone = this.injector.get(NgZone);\n      assertNonEmptyInput(this, 'ngSrc', this.ngSrc);\n      assertValidNgSrcset(this, this.ngSrcset);\n      assertNoConflictingSrc(this);\n      if (this.ngSrcset) {\n        assertNoConflictingSrcset(this);\n      }\n      assertNotBase64Image(this);\n      assertNotBlobUrl(this);\n      if (this.fill) {\n        assertEmptyWidthAndHeight(this);\n        // This leaves the Angular zone to avoid triggering unnecessary change detection cycles when\n        // `load` tasks are invoked on images.\n        ngZone.runOutsideAngular(() => assertNonZeroRenderedHeight(this, this.imgElement, this.renderer));\n      } else {\n        assertNonEmptyWidthAndHeight(this);\n        if (this.height !== undefined) {\n          assertGreaterThanZero(this, this.height, 'height');\n        }\n        if (this.width !== undefined) {\n          assertGreaterThanZero(this, this.width, 'width');\n        }\n        // Only check for distorted images when not in fill mode, where\n        // images may be intentionally stretched, cropped or letterboxed.\n        ngZone.runOutsideAngular(() => assertNoImageDistortion(this, this.imgElement, this.renderer));\n      }\n      assertValidLoadingInput(this);\n      if (!this.ngSrcset) {\n        assertNoComplexSizes(this);\n      }\n      assertValidPlaceholder(this, this.imageLoader);\n      assertNotMissingBuiltInLoader(this.ngSrc, this.imageLoader);\n      assertNoNgSrcsetWithoutLoader(this, this.imageLoader);\n      assertNoLoaderParamsWithoutLoader(this, this.imageLoader);\n      ngZone.runOutsideAngular(() => {\n        this.lcpObserver.registerImage(this.getRewrittenSrc(), this.ngSrc, this.priority);\n      });\n      if (this.priority) {\n        const checker = this.injector.get(PreconnectLinkChecker);\n        checker.assertPreconnect(this.getRewrittenSrc(), this.ngSrc);\n        if (typeof ngServerMode !== 'undefined' && !ngServerMode) {\n          const applicationRef = this.injector.get(ApplicationRef);\n          assetPriorityCountBelowThreshold(applicationRef);\n        }\n      }\n    }\n    if (this.placeholder) {\n      this.removePlaceholderOnLoad(this.imgElement);\n    }\n    this.setHostAttributes();\n  }\n  setHostAttributes() {\n    // Must set width/height explicitly in case they are bound (in which case they will\n    // only be reflected and not found by the browser)\n    if (this.fill) {\n      this.sizes ||= '100vw';\n    } else {\n      this.setHostAttribute('width', this.width.toString());\n      this.setHostAttribute('height', this.height.toString());\n    }\n    this.setHostAttribute('loading', this.getLoadingBehavior());\n    this.setHostAttribute('fetchpriority', this.getFetchPriority());\n    // The `data-ng-img` attribute flags an image as using the directive, to allow\n    // for analysis of the directive's performance.\n    this.setHostAttribute('ng-img', 'true');\n    // The `src` and `srcset` attributes should be set last since other attributes\n    // could affect the image's loading behavior.\n    const rewrittenSrcset = this.updateSrcAndSrcset();\n    if (this.sizes) {\n      if (this.getLoadingBehavior() === 'lazy') {\n        this.setHostAttribute('sizes', 'auto, ' + this.sizes);\n      } else {\n        this.setHostAttribute('sizes', this.sizes);\n      }\n    } else {\n      if (this.ngSrcset && VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset) && this.getLoadingBehavior() === 'lazy') {\n        this.setHostAttribute('sizes', 'auto, 100vw');\n      }\n    }\n    if (typeof ngServerMode !== 'undefined' && ngServerMode && this.priority) {\n      const preloadLinkCreator = this.injector.get(PreloadLinkCreator);\n      preloadLinkCreator.createPreloadLinkTag(this.renderer, this.getRewrittenSrc(), rewrittenSrcset, this.sizes);\n    }\n  }\n  /** @docs-private */\n  ngOnChanges(changes) {\n    if (ngDevMode) {\n      assertNoPostInitInputChange(this, changes, ['ngSrcset', 'width', 'height', 'priority', 'fill', 'loading', 'sizes', 'loaderParams', 'disableOptimizedSrcset']);\n    }\n    if (changes['ngSrc'] && !changes['ngSrc'].isFirstChange()) {\n      const oldSrc = this._renderedSrc;\n      this.updateSrcAndSrcset(true);\n      if (ngDevMode) {\n        const newSrc = this._renderedSrc;\n        if (oldSrc && newSrc && oldSrc !== newSrc) {\n          const ngZone = this.injector.get(NgZone);\n          ngZone.runOutsideAngular(() => {\n            this.lcpObserver.updateImage(oldSrc, newSrc);\n          });\n        }\n      }\n    }\n    if (ngDevMode && changes['placeholder']?.currentValue && typeof ngServerMode !== 'undefined' && !ngServerMode) {\n      assertPlaceholderDimensions(this, this.imgElement);\n    }\n  }\n  callImageLoader(configWithoutCustomParams) {\n    let augmentedConfig = configWithoutCustomParams;\n    if (this.loaderParams) {\n      augmentedConfig.loaderParams = this.loaderParams;\n    }\n    return this.imageLoader(augmentedConfig);\n  }\n  getLoadingBehavior() {\n    if (!this.priority && this.loading !== undefined) {\n      return this.loading;\n    }\n    return this.priority ? 'eager' : 'lazy';\n  }\n  getFetchPriority() {\n    return this.priority ? 'high' : 'auto';\n  }\n  getRewrittenSrc() {\n    // ImageLoaderConfig supports setting a width property. However, we're not setting width here\n    // because if the developer uses rendered width instead of intrinsic width in the HTML width\n    // attribute, the image requested may be too small for 2x+ screens.\n    if (!this._renderedSrc) {\n      const imgConfig = {\n        src: this.ngSrc\n      };\n      // Cache calculated image src to reuse it later in the code.\n      this._renderedSrc = this.callImageLoader(imgConfig);\n    }\n    return this._renderedSrc;\n  }\n  getRewrittenSrcset() {\n    const widthSrcSet = VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset);\n    const finalSrcs = this.ngSrcset.split(',').filter(src => src !== '').map(srcStr => {\n      srcStr = srcStr.trim();\n      const width = widthSrcSet ? parseFloat(srcStr) : parseFloat(srcStr) * this.width;\n      return `${this.callImageLoader({\n        src: this.ngSrc,\n        width\n      })} ${srcStr}`;\n    });\n    return finalSrcs.join(', ');\n  }\n  getAutomaticSrcset() {\n    if (this.sizes) {\n      return this.getResponsiveSrcset();\n    } else {\n      return this.getFixedSrcset();\n    }\n  }\n  getResponsiveSrcset() {\n    const {\n      breakpoints\n    } = this.config;\n    let filteredBreakpoints = breakpoints;\n    if (this.sizes?.trim() === '100vw') {\n      // Since this is a full-screen-width image, our srcset only needs to include\n      // breakpoints with full viewport widths.\n      filteredBreakpoints = breakpoints.filter(bp => bp >= VIEWPORT_BREAKPOINT_CUTOFF);\n    }\n    const finalSrcs = filteredBreakpoints.map(bp => `${this.callImageLoader({\n      src: this.ngSrc,\n      width: bp\n    })} ${bp}w`);\n    return finalSrcs.join(', ');\n  }\n  updateSrcAndSrcset(forceSrcRecalc = false) {\n    if (forceSrcRecalc) {\n      // Reset cached value, so that the followup `getRewrittenSrc()` call\n      // will recalculate it and update the cache.\n      this._renderedSrc = null;\n    }\n    const rewrittenSrc = this.getRewrittenSrc();\n    this.setHostAttribute('src', rewrittenSrc);\n    let rewrittenSrcset = undefined;\n    if (this.ngSrcset) {\n      rewrittenSrcset = this.getRewrittenSrcset();\n    } else if (this.shouldGenerateAutomaticSrcset()) {\n      rewrittenSrcset = this.getAutomaticSrcset();\n    }\n    if (rewrittenSrcset) {\n      this.setHostAttribute('srcset', rewrittenSrcset);\n    }\n    return rewrittenSrcset;\n  }\n  getFixedSrcset() {\n    const finalSrcs = DENSITY_SRCSET_MULTIPLIERS.map(multiplier => `${this.callImageLoader({\n      src: this.ngSrc,\n      width: this.width * multiplier\n    })} ${multiplier}x`);\n    return finalSrcs.join(', ');\n  }\n  shouldGenerateAutomaticSrcset() {\n    let oversizedImage = false;\n    if (!this.sizes) {\n      oversizedImage = this.width > FIXED_SRCSET_WIDTH_LIMIT || this.height > FIXED_SRCSET_HEIGHT_LIMIT;\n    }\n    return !this.disableOptimizedSrcset && !this.srcset && this.imageLoader !== noopImageLoader && !oversizedImage;\n  }\n  /**\n   * Returns an image url formatted for use with the CSS background-image property. Expects one of:\n   * * A base64 encoded image, which is wrapped and passed through.\n   * * A boolean. If true, calls the image loader to generate a small placeholder url.\n   */\n  generatePlaceholder(placeholderInput) {\n    const {\n      placeholderResolution\n    } = this.config;\n    if (placeholderInput === true) {\n      return `url(${this.callImageLoader({\n        src: this.ngSrc,\n        width: placeholderResolution,\n        isPlaceholder: true\n      })})`;\n    } else if (typeof placeholderInput === 'string') {\n      return `url(${placeholderInput})`;\n    }\n    return null;\n  }\n  /**\n   * Determines if blur should be applied, based on an optional boolean\n   * property `blur` within the optional configuration object `placeholderConfig`.\n   */\n  shouldBlurPlaceholder(placeholderConfig) {\n    if (!placeholderConfig || !placeholderConfig.hasOwnProperty('blur')) {\n      return true;\n    }\n    return Boolean(placeholderConfig.blur);\n  }\n  removePlaceholderOnLoad(img) {\n    const callback = () => {\n      const changeDetectorRef = this.injector.get(ChangeDetectorRef);\n      removeLoadListenerFn();\n      removeErrorListenerFn();\n      this.placeholder = false;\n      changeDetectorRef.markForCheck();\n    };\n    const removeLoadListenerFn = this.renderer.listen(img, 'load', callback);\n    const removeErrorListenerFn = this.renderer.listen(img, 'error', callback);\n    callOnLoadIfImageIsLoaded(img, callback);\n  }\n  setHostAttribute(name, value) {\n    this.renderer.setAttribute(this.imgElement, name, value);\n  }\n  static ɵfac = function NgOptimizedImage_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NgOptimizedImage)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: NgOptimizedImage,\n    selectors: [[\"img\", \"ngSrc\", \"\"]],\n    hostVars: 18,\n    hostBindings: function NgOptimizedImage_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"position\", ctx.fill ? \"absolute\" : null)(\"width\", ctx.fill ? \"100%\" : null)(\"height\", ctx.fill ? \"100%\" : null)(\"inset\", ctx.fill ? \"0\" : null)(\"background-size\", ctx.placeholder ? \"cover\" : null)(\"background-position\", ctx.placeholder ? \"50% 50%\" : null)(\"background-repeat\", ctx.placeholder ? \"no-repeat\" : null)(\"background-image\", ctx.placeholder ? ctx.generatePlaceholder(ctx.placeholder) : null)(\"filter\", ctx.placeholder && ctx.shouldBlurPlaceholder(ctx.placeholderConfig) ? \"blur(15px)\" : null);\n      }\n    },\n    inputs: {\n      ngSrc: [2, \"ngSrc\", \"ngSrc\", unwrapSafeUrl],\n      ngSrcset: \"ngSrcset\",\n      sizes: \"sizes\",\n      width: [2, \"width\", \"width\", numberAttribute],\n      height: [2, \"height\", \"height\", numberAttribute],\n      loading: \"loading\",\n      priority: [2, \"priority\", \"priority\", booleanAttribute],\n      loaderParams: \"loaderParams\",\n      disableOptimizedSrcset: [2, \"disableOptimizedSrcset\", \"disableOptimizedSrcset\", booleanAttribute],\n      fill: [2, \"fill\", \"fill\", booleanAttribute],\n      placeholder: [2, \"placeholder\", \"placeholder\", booleanOrUrlAttribute],\n      placeholderConfig: \"placeholderConfig\",\n      src: \"src\",\n      srcset: \"srcset\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NgOptimizedImage, [{\n    type: Directive,\n    args: [{\n      selector: 'img[ngSrc]',\n      host: {\n        '[style.position]': 'fill ? \"absolute\" : null',\n        '[style.width]': 'fill ? \"100%\" : null',\n        '[style.height]': 'fill ? \"100%\" : null',\n        '[style.inset]': 'fill ? \"0\" : null',\n        '[style.background-size]': 'placeholder ? \"cover\" : null',\n        '[style.background-position]': 'placeholder ? \"50% 50%\" : null',\n        '[style.background-repeat]': 'placeholder ? \"no-repeat\" : null',\n        '[style.background-image]': 'placeholder ? generatePlaceholder(placeholder) : null',\n        '[style.filter]': `placeholder && shouldBlurPlaceholder(placeholderConfig) ? \"blur(${PLACEHOLDER_BLUR_AMOUNT}px)\" : null`\n      }\n    }]\n  }], () => [], {\n    ngSrc: [{\n      type: Input,\n      args: [{\n        required: true,\n        transform: unwrapSafeUrl\n      }]\n    }],\n    ngSrcset: [{\n      type: Input\n    }],\n    sizes: [{\n      type: Input\n    }],\n    width: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    height: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    loading: [{\n      type: Input\n    }],\n    priority: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    loaderParams: [{\n      type: Input\n    }],\n    disableOptimizedSrcset: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    fill: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input,\n      args: [{\n        transform: booleanOrUrlAttribute\n      }]\n    }],\n    placeholderConfig: [{\n      type: Input\n    }],\n    src: [{\n      type: Input\n    }],\n    srcset: [{\n      type: Input\n    }]\n  });\n})();\n/***** Helpers *****/\n/**\n * Sorts provided config breakpoints and uses defaults.\n */\nfunction processConfig(config) {\n  let sortedBreakpoints = {};\n  if (config.breakpoints) {\n    sortedBreakpoints.breakpoints = config.breakpoints.sort((a, b) => a - b);\n  }\n  return Object.assign({}, _IMAGE_CONFIG_DEFAULTS, config, sortedBreakpoints);\n}\n/***** Assert functions *****/\n/**\n * Verifies that there is no `src` set on a host element.\n */\nfunction assertNoConflictingSrc(dir) {\n  if (dir.src) {\n    throw new _RuntimeError(2950 /* RuntimeErrorCode.UNEXPECTED_SRC_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`src\\` and \\`ngSrc\\` have been set. ` + `Supplying both of these attributes breaks lazy loading. ` + `The NgOptimizedImage directive sets \\`src\\` itself based on the value of \\`ngSrc\\`. ` + `To fix this, please remove the \\`src\\` attribute.`);\n  }\n}\n/**\n * Verifies that there is no `srcset` set on a host element.\n */\nfunction assertNoConflictingSrcset(dir) {\n  if (dir.srcset) {\n    throw new _RuntimeError(2951 /* RuntimeErrorCode.UNEXPECTED_SRCSET_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`srcset\\` and \\`ngSrcset\\` have been set. ` + `Supplying both of these attributes breaks lazy loading. ` + `The NgOptimizedImage directive sets \\`srcset\\` itself based on the value of ` + `\\`ngSrcset\\`. To fix this, please remove the \\`srcset\\` attribute.`);\n  }\n}\n/**\n * Verifies that the `ngSrc` is not a Base64-encoded image.\n */\nfunction assertNotBase64Image(dir) {\n  let ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('data:')) {\n    if (ngSrc.length > BASE64_IMG_MAX_LENGTH_IN_ERROR) {\n      ngSrc = ngSrc.substring(0, BASE64_IMG_MAX_LENGTH_IN_ERROR) + '...';\n    }\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`ngSrc\\` is a Base64-encoded string ` + `(${ngSrc}). NgOptimizedImage does not support Base64-encoded strings. ` + `To fix this, disable the NgOptimizedImage directive for this element ` + `by removing \\`ngSrc\\` and using a standard \\`src\\` attribute instead.`);\n  }\n}\n/**\n * Verifies that the 'sizes' only includes responsive values.\n */\nfunction assertNoComplexSizes(dir) {\n  let sizes = dir.sizes;\n  if (sizes?.match(/((\\)|,)\\s|^)\\d+px/)) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`sizes\\` was set to a string including ` + `pixel values. For automatic \\`srcset\\` generation, \\`sizes\\` must only include responsive ` + `values, such as \\`sizes=\"50vw\"\\` or \\`sizes=\"(min-width: 768px) 50vw, 100vw\"\\`. ` + `To fix this, modify the \\`sizes\\` attribute, or provide your own \\`ngSrcset\\` value directly.`);\n  }\n}\nfunction assertValidPlaceholder(dir, imageLoader) {\n  assertNoPlaceholderConfigWithoutPlaceholder(dir);\n  assertNoRelativePlaceholderWithoutLoader(dir, imageLoader);\n  assertNoOversizedDataUrl(dir);\n}\n/**\n * Verifies that placeholderConfig isn't being used without placeholder\n */\nfunction assertNoPlaceholderConfigWithoutPlaceholder(dir) {\n  if (dir.placeholderConfig && !dir.placeholder) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`placeholderConfig\\` options were provided for an ` + `image that does not use the \\`placeholder\\` attribute, and will have no effect.`);\n  }\n}\n/**\n * Warns if a relative URL placeholder is specified, but no loader is present to provide the small\n * image.\n */\nfunction assertNoRelativePlaceholderWithoutLoader(dir, imageLoader) {\n  if (dir.placeholder === true && imageLoader === noopImageLoader) {\n    throw new _RuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to true but ` + `no image loader is configured (i.e. the default one is being used), ` + `which would result in the same image being used for the primary image and its placeholder. ` + `To fix this, provide a loader or remove the \\`placeholder\\` attribute from the image.`);\n  }\n}\n/**\n * Warns or throws an error if an oversized dataURL placeholder is provided.\n */\nfunction assertNoOversizedDataUrl(dir) {\n  if (dir.placeholder && typeof dir.placeholder === 'string' && dir.placeholder.startsWith('data:')) {\n    if (dir.placeholder.length > DATA_URL_ERROR_LIMIT) {\n      throw new _RuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` + `than ${DATA_URL_ERROR_LIMIT} characters. This is strongly discouraged, as large inline placeholders ` + `directly increase the bundle size of Angular and hurt page load performance. To fix this, generate ` + `a smaller data URL placeholder.`);\n    }\n    if (dir.placeholder.length > DATA_URL_WARN_LIMIT) {\n      console.warn(_formatRuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` + `than ${DATA_URL_WARN_LIMIT} characters. This is discouraged, as large inline placeholders ` + `directly increase the bundle size of Angular and hurt page load performance. For better loading performance, ` + `generate a smaller data URL placeholder.`));\n    }\n  }\n}\n/**\n * Verifies that the `ngSrc` is not a Blob URL.\n */\nfunction assertNotBlobUrl(dir) {\n  const ngSrc = dir.ngSrc.trim();\n  if (ngSrc.startsWith('blob:')) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrc\\` was set to a blob URL (${ngSrc}). ` + `Blob URLs are not supported by the NgOptimizedImage directive. ` + `To fix this, disable the NgOptimizedImage directive for this element ` + `by removing \\`ngSrc\\` and using a regular \\`src\\` attribute instead.`);\n  }\n}\n/**\n * Verifies that the input is set to a non-empty string.\n */\nfunction assertNonEmptyInput(dir, name, value) {\n  const isString = typeof value === 'string';\n  const isEmptyString = isString && value.trim() === '';\n  if (!isString || isEmptyString) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${name}\\` has an invalid value ` + `(\\`${value}\\`). To fix this, change the value to a non-empty string.`);\n  }\n}\n/**\n * Verifies that the `ngSrcset` is in a valid format, e.g. \"100w, 200w\" or \"1x, 2x\".\n */\nfunction assertValidNgSrcset(dir, value) {\n  if (value == null) return;\n  assertNonEmptyInput(dir, 'ngSrcset', value);\n  const stringVal = value;\n  const isValidWidthDescriptor = VALID_WIDTH_DESCRIPTOR_SRCSET.test(stringVal);\n  const isValidDensityDescriptor = VALID_DENSITY_DESCRIPTOR_SRCSET.test(stringVal);\n  if (isValidDensityDescriptor) {\n    assertUnderDensityCap(dir, stringVal);\n  }\n  const isValidSrcset = isValidWidthDescriptor || isValidDensityDescriptor;\n  if (!isValidSrcset) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrcset\\` has an invalid value (\\`${value}\\`). ` + `To fix this, supply \\`ngSrcset\\` using a comma-separated list of one or more width ` + `descriptors (e.g. \"100w, 200w\") or density descriptors (e.g. \"1x, 2x\").`);\n  }\n}\nfunction assertUnderDensityCap(dir, value) {\n  const underDensityCap = value.split(',').every(num => num === '' || parseFloat(num) <= ABSOLUTE_SRCSET_DENSITY_CAP);\n  if (!underDensityCap) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` contains an unsupported image density:` + `\\`${value}\\`. NgOptimizedImage generally recommends a max image density of ` + `${RECOMMENDED_SRCSET_DENSITY_CAP}x but supports image densities up to ` + `${ABSOLUTE_SRCSET_DENSITY_CAP}x. The human eye cannot distinguish between image densities ` + `greater than ${RECOMMENDED_SRCSET_DENSITY_CAP}x - which makes them unnecessary for ` + `most use cases. Images that will be pinch-zoomed are typically the primary use case for ` + `${ABSOLUTE_SRCSET_DENSITY_CAP}x images. Please remove the high density descriptor and try again.`);\n  }\n}\n/**\n * Creates a `RuntimeError` instance to represent a situation when an input is set after\n * the directive has initialized.\n */\nfunction postInitInputChangeError(dir, inputName) {\n  let reason;\n  if (inputName === 'width' || inputName === 'height') {\n    reason = `Changing \\`${inputName}\\` may result in different attribute value ` + `applied to the underlying image element and cause layout shifts on a page.`;\n  } else {\n    reason = `Changing the \\`${inputName}\\` would have no effect on the underlying ` + `image element, because the resource loading has already occurred.`;\n  }\n  return new _RuntimeError(2953 /* RuntimeErrorCode.UNEXPECTED_INPUT_CHANGE */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` was updated after initialization. ` + `The NgOptimizedImage directive will not react to this input change. ${reason} ` + `To fix this, either switch \\`${inputName}\\` to a static value ` + `or wrap the image element in an @if that is gated on the necessary value.`);\n}\n/**\n * Verify that none of the listed inputs has changed.\n */\nfunction assertNoPostInitInputChange(dir, changes, inputs) {\n  inputs.forEach(input => {\n    const isUpdated = changes.hasOwnProperty(input);\n    if (isUpdated && !changes[input].isFirstChange()) {\n      if (input === 'ngSrc') {\n        // When the `ngSrc` input changes, we detect that only in the\n        // `ngOnChanges` hook, thus the `ngSrc` is already set. We use\n        // `ngSrc` in the error message, so we use a previous value, but\n        // not the updated one in it.\n        dir = {\n          ngSrc: changes[input].previousValue\n        };\n      }\n      throw postInitInputChangeError(dir, input);\n    }\n  });\n}\n/**\n * Verifies that a specified input is a number greater than 0.\n */\nfunction assertGreaterThanZero(dir, inputValue, inputName) {\n  const validNumber = typeof inputValue === 'number' && inputValue > 0;\n  const validString = typeof inputValue === 'string' && /^\\d+$/.test(inputValue.trim()) && parseInt(inputValue) > 0;\n  if (!validNumber && !validString) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` has an invalid value. ` + `To fix this, provide \\`${inputName}\\` as a number greater than 0.`);\n  }\n}\n/**\n * Verifies that the rendered image is not visually distorted. Effectively this is checking:\n * - Whether the \"width\" and \"height\" attributes reflect the actual dimensions of the image.\n * - Whether image styling is \"correct\" (see below for a longer explanation).\n */\nfunction assertNoImageDistortion(dir, img, renderer) {\n  const callback = () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const computedStyle = window.getComputedStyle(img);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    const boxSizing = computedStyle.getPropertyValue('box-sizing');\n    if (boxSizing === 'border-box') {\n      const paddingTop = computedStyle.getPropertyValue('padding-top');\n      const paddingRight = computedStyle.getPropertyValue('padding-right');\n      const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n      const paddingLeft = computedStyle.getPropertyValue('padding-left');\n      renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n      renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n    }\n    const renderedAspectRatio = renderedWidth / renderedHeight;\n    const nonZeroRenderedDimensions = renderedWidth !== 0 && renderedHeight !== 0;\n    const intrinsicWidth = img.naturalWidth;\n    const intrinsicHeight = img.naturalHeight;\n    const intrinsicAspectRatio = intrinsicWidth / intrinsicHeight;\n    const suppliedWidth = dir.width;\n    const suppliedHeight = dir.height;\n    const suppliedAspectRatio = suppliedWidth / suppliedHeight;\n    // Tolerance is used to account for the impact of subpixel rendering.\n    // Due to subpixel rendering, the rendered, intrinsic, and supplied\n    // aspect ratios of a correctly configured image may not exactly match.\n    // For example, a `width=4030 height=3020` image might have a rendered\n    // size of \"1062w, 796.48h\". (An aspect ratio of 1.334... vs. 1.333...)\n    const inaccurateDimensions = Math.abs(suppliedAspectRatio - intrinsicAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    const stylingDistortion = nonZeroRenderedDimensions && Math.abs(intrinsicAspectRatio - renderedAspectRatio) > ASPECT_RATIO_TOLERANCE;\n    if (inaccurateDimensions) {\n      console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the image does not match ` + `the aspect ratio indicated by the width and height attributes. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` + `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nSupplied width and height attributes: ` + `${suppliedWidth}w x ${suppliedHeight}h (aspect-ratio: ${round(suppliedAspectRatio)}). ` + `\\nTo fix this, update the width and height attributes.`));\n    } else if (stylingDistortion) {\n      console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the rendered image ` + `does not match the image's intrinsic aspect ratio. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` + `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nRendered image size: ` + `${renderedWidth}w x ${renderedHeight}h (aspect-ratio: ` + `${round(renderedAspectRatio)}). \\nThis issue can occur if \"width\" and \"height\" ` + `attributes are added to an image without updating the corresponding ` + `image styling. To fix this, adjust image styling. In most cases, ` + `adding \"height: auto\" or \"width: auto\" to the image styling will fix ` + `this issue.`));\n    } else if (!dir.ngSrcset && nonZeroRenderedDimensions) {\n      // If `ngSrcset` hasn't been set, sanity check the intrinsic size.\n      const recommendedWidth = RECOMMENDED_SRCSET_DENSITY_CAP * renderedWidth;\n      const recommendedHeight = RECOMMENDED_SRCSET_DENSITY_CAP * renderedHeight;\n      const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n      const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n      if (oversizedWidth || oversizedHeight) {\n        console.warn(_formatRuntimeError(2960 /* RuntimeErrorCode.OVERSIZED_IMAGE */, `${imgDirectiveDetails(dir.ngSrc)} the intrinsic image is significantly ` + `larger than necessary. ` + `\\nRendered image size: ${renderedWidth}w x ${renderedHeight}h. ` + `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h. ` + `\\nRecommended intrinsic image size: ${recommendedWidth}w x ${recommendedHeight}h. ` + `\\nNote: Recommended intrinsic image size is calculated assuming a maximum DPR of ` + `${RECOMMENDED_SRCSET_DENSITY_CAP}. To improve loading time, resize the image ` + `or consider using the \"ngSrcset\" and \"sizes\" attributes.`));\n      }\n    }\n  };\n  const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n  // We only listen to the `error` event to remove the `load` event listener because it will not be\n  // fired if the image fails to load. This is done to prevent memory leaks in development mode\n  // because image elements aren't garbage-collected properly. It happens because zone.js stores the\n  // event listener directly on the element and closures capture `dir`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n  callOnLoadIfImageIsLoaded(img, callback);\n}\n/**\n * Verifies that a specified input is set.\n */\nfunction assertNonEmptyWidthAndHeight(dir) {\n  let missingAttributes = [];\n  if (dir.width === undefined) missingAttributes.push('width');\n  if (dir.height === undefined) missingAttributes.push('height');\n  if (missingAttributes.length > 0) {\n    throw new _RuntimeError(2954 /* RuntimeErrorCode.REQUIRED_INPUT_MISSING */, `${imgDirectiveDetails(dir.ngSrc)} these required attributes ` + `are missing: ${missingAttributes.map(attr => `\"${attr}\"`).join(', ')}. ` + `Including \"width\" and \"height\" attributes will prevent image-related layout shifts. ` + `To fix this, include \"width\" and \"height\" attributes on the image tag or turn on ` + `\"fill\" mode with the \\`fill\\` attribute.`);\n  }\n}\n/**\n * Verifies that width and height are not set. Used in fill mode, where those attributes don't make\n * sense.\n */\nfunction assertEmptyWidthAndHeight(dir) {\n  if (dir.width || dir.height) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the attributes \\`height\\` and/or \\`width\\` are present ` + `along with the \\`fill\\` attribute. Because \\`fill\\` mode causes an image to fill its containing ` + `element, the size attributes have no effect and should be removed.`);\n  }\n}\n/**\n * Verifies that the rendered image has a nonzero height. If the image is in fill mode, provides\n * guidance that this can be caused by the containing element's CSS position property.\n */\nfunction assertNonZeroRenderedHeight(dir, img, renderer) {\n  const callback = () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n    const renderedHeight = img.clientHeight;\n    if (dir.fill && renderedHeight === 0) {\n      console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the height of the fill-mode image is zero. ` + `This is likely because the containing element does not have the CSS 'position' ` + `property set to one of the following: \"relative\", \"fixed\", or \"absolute\". ` + `To fix this problem, make sure the container element has the CSS 'position' ` + `property defined and the height of the element is not zero.`));\n    }\n  };\n  const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n  // See comments in the `assertNoImageDistortion`.\n  const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n    removeLoadListenerFn();\n    removeErrorListenerFn();\n  });\n  callOnLoadIfImageIsLoaded(img, callback);\n}\n/**\n * Verifies that the `loading` attribute is set to a valid input &\n * is not used on priority images.\n */\nfunction assertValidLoadingInput(dir) {\n  if (dir.loading && dir.priority) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` + `was used on an image that was marked \"priority\". ` + `Setting \\`loading\\` on priority images is not allowed ` + `because these images will always be eagerly loaded. ` + `To fix this, remove the “loading” attribute from the priority image.`);\n  }\n  const validInputs = ['auto', 'eager', 'lazy'];\n  if (typeof dir.loading === 'string' && !validInputs.includes(dir.loading)) {\n    throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` + `has an invalid value (\\`${dir.loading}\\`). ` + `To fix this, provide a valid value (\"lazy\", \"eager\", or \"auto\").`);\n  }\n}\n/**\n * Warns if NOT using a loader (falling back to the generic loader) and\n * the image appears to be hosted on one of the image CDNs for which\n * we do have a built-in image loader. Suggests switching to the\n * built-in loader.\n *\n * @param ngSrc Value of the ngSrc attribute\n * @param imageLoader ImageLoader provided\n */\nfunction assertNotMissingBuiltInLoader(ngSrc, imageLoader) {\n  if (imageLoader === noopImageLoader) {\n    let builtInLoaderName = '';\n    for (const loader of BUILT_IN_LOADERS) {\n      if (loader.testUrl(ngSrc)) {\n        builtInLoaderName = loader.name;\n        break;\n      }\n    }\n    if (builtInLoaderName) {\n      console.warn(_formatRuntimeError(2962 /* RuntimeErrorCode.MISSING_BUILTIN_LOADER */, `NgOptimizedImage: It looks like your images may be hosted on the ` + `${builtInLoaderName} CDN, but your app is not using Angular's ` + `built-in loader for that CDN. We recommend switching to use ` + `the built-in by calling \\`provide${builtInLoaderName}Loader()\\` ` + `in your \\`providers\\` and passing it your instance's base URL. ` + `If you don't want to use the built-in loader, define a custom ` + `loader function using IMAGE_LOADER to silence this warning.`));\n    }\n  }\n}\n/**\n * Warns if ngSrcset is present and no loader is configured (i.e. the default one is being used).\n */\nfunction assertNoNgSrcsetWithoutLoader(dir, imageLoader) {\n  if (dir.ngSrcset && imageLoader === noopImageLoader) {\n    console.warn(_formatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` attribute is present but ` + `no image loader is configured (i.e. the default one is being used), ` + `which would result in the same image being used for all configured sizes. ` + `To fix this, provide a loader or remove the \\`ngSrcset\\` attribute from the image.`));\n  }\n}\n/**\n * Warns if loaderParams is present and no loader is configured (i.e. the default one is being\n * used).\n */\nfunction assertNoLoaderParamsWithoutLoader(dir, imageLoader) {\n  if (dir.loaderParams && imageLoader === noopImageLoader) {\n    console.warn(_formatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loaderParams\\` attribute is present but ` + `no image loader is configured (i.e. the default one is being used), ` + `which means that the loaderParams data will not be consumed and will not affect the URL. ` + `To fix this, provide a custom loader or remove the \\`loaderParams\\` attribute from the image.`));\n  }\n}\n/**\n * Warns if the priority attribute is used too often on page load\n */\nfunction assetPriorityCountBelowThreshold(_x) {\n  return _assetPriorityCountBelowThreshold.apply(this, arguments);\n}\n/**\n * Warns if placeholder's dimension are over a threshold.\n *\n * This assert function is meant to only run on the browser.\n */\nfunction _assetPriorityCountBelowThreshold() {\n  _assetPriorityCountBelowThreshold = _asyncToGenerator(function* (appRef) {\n    if (IMGS_WITH_PRIORITY_ATTR_COUNT === 0) {\n      IMGS_WITH_PRIORITY_ATTR_COUNT++;\n      yield appRef.whenStable();\n      if (IMGS_WITH_PRIORITY_ATTR_COUNT > PRIORITY_COUNT_THRESHOLD) {\n        console.warn(_formatRuntimeError(2966 /* RuntimeErrorCode.TOO_MANY_PRIORITY_ATTRIBUTES */, `NgOptimizedImage: The \"priority\" attribute is set to true more than ${PRIORITY_COUNT_THRESHOLD} times (${IMGS_WITH_PRIORITY_ATTR_COUNT} times). ` + `Marking too many images as \"high\" priority can hurt your application's LCP (https://web.dev/lcp). ` + `\"Priority\" should only be set on the image expected to be the page's LCP element.`));\n      }\n    } else {\n      IMGS_WITH_PRIORITY_ATTR_COUNT++;\n    }\n  });\n  return _assetPriorityCountBelowThreshold.apply(this, arguments);\n}\nfunction assertPlaceholderDimensions(dir, imgElement) {\n  const computedStyle = window.getComputedStyle(imgElement);\n  let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n  let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n  if (renderedWidth > PLACEHOLDER_DIMENSION_LIMIT || renderedHeight > PLACEHOLDER_DIMENSION_LIMIT) {\n    console.warn(_formatRuntimeError(2967 /* RuntimeErrorCode.PLACEHOLDER_DIMENSION_LIMIT_EXCEEDED */, `${imgDirectiveDetails(dir.ngSrc)} it uses a placeholder image, but at least one ` + `of the dimensions attribute (height or width) exceeds the limit of ${PLACEHOLDER_DIMENSION_LIMIT}px. ` + `To fix this, use a smaller image as a placeholder.`));\n  }\n}\nfunction callOnLoadIfImageIsLoaded(img, callback) {\n  // https://html.spec.whatwg.org/multipage/embedded-content.html#dom-img-complete\n  // The spec defines that `complete` is truthy once its request state is fully available.\n  // The image may already be available if it’s loaded from the browser cache.\n  // In that case, the `load` event will not fire at all, meaning that all setup\n  // callbacks listening for the `load` event will not be invoked.\n  // In Safari, there is a known behavior where the `complete` property of an\n  // `HTMLImageElement` may sometimes return `true` even when the image is not fully loaded.\n  // Checking both `img.complete` and `img.naturalWidth` is the most reliable way to\n  // determine if an image has been fully loaded, especially in browsers where the\n  // `complete` property may return `true` prematurely.\n  if (img.complete && img.naturalWidth) {\n    callback();\n  }\n}\nfunction round(input) {\n  return Number.isInteger(input) ? input : input.toFixed(2);\n}\n// Transform function to handle SafeValue input for ngSrc. This doesn't do any sanitization,\n// as that is not needed for img.src and img.srcset. This transform is purely for compatibility.\nfunction unwrapSafeUrl(value) {\n  if (typeof value === 'string') {\n    return value;\n  }\n  return _unwrapSafeValue(value);\n}\n// Transform function to handle inputs which may be booleans, strings, or string representations\n// of boolean values. Used for the placeholder attribute.\nfunction booleanOrUrlAttribute(value) {\n  if (typeof value === 'string' && value !== 'true' && value !== 'false' && value !== '') {\n    return value;\n  }\n  return booleanAttribute(value);\n}\nexport { DOCUMENT, IMAGE_LOADER, NgOptimizedImage, PRECONNECT_CHECK_BLOCKLIST, VERSION, ViewportScroller, isPlatformBrowser, provideCloudflareLoader, provideCloudinaryLoader, provideImageKitLoader, provideImgixLoader, provideNetlifyLoader, registerLocaleData, NullViewportScroller as ɵNullViewportScroller };", "map": {"version": 3, "names": ["AsyncPipe", "CommonModule", "C<PERSON><PERSON>cyPipe", "DATE_PIPE_DEFAULT_OPTIONS", "DATE_PIPE_DEFAULT_TIMEZONE", "DatePipe", "DecimalPipe", "FormStyle", "FormatWidth", "HashLocationStrategy", "I18nPluralPipe", "I18nSelectPipe", "JsonPipe", "KeyValuePipe", "LowerCasePipe", "Ng<PERSON><PERSON>", "NgComponentOutlet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "NgForOfContext", "NgIf", "NgIfContext", "NgLocaleLocalization", "NgLocalization", "Ng<PERSON><PERSON><PERSON>", "NgPluralCase", "NgStyle", "NgSwitch", "NgSwitchCase", "NgSwitchDefault", "NgTemplateOutlet", "NumberFormatStyle", "NumberSymbol", "Percent<PERSON><PERSON>e", "Plural", "SlicePipe", "TitleCasePipe", "TranslationWidth", "UpperCasePipe", "WeekDay", "formatCurrency", "formatDate", "formatNumber", "formatPercent", "getCurrencySymbol", "getLocaleCurrencyCode", "getLocaleCurrencyName", "getLocaleCurrencySymbol", "getLocaleDateFormat", "getLocaleDateTimeFormat", "getLocaleDayNames", "getLocaleDayPeriods", "getLocaleDirection", "getLocaleEraNames", "getLocaleExtraDayPeriodRules", "getLocaleExtraDayPeriods", "getLocaleFirstDayOfWeek", "getLocaleId", "getLocaleMonthNames", "getLocaleNumberFormat", "getLocaleNumberSymbol", "getLocalePluralCase", "getLocaleTimeFormat", "getLocaleWeekEndRange", "getNumberOfCurrencyDigits", "i0", "ɵregisterLocaleData", "_registerLocaleData", "Version", "ɵɵdefineInjectable", "__defineInjectable", "inject", "InjectionToken", "ɵRuntimeError", "_RuntimeError", "ɵformatRuntimeError", "_formatRuntimeError", "PLATFORM_ID", "Injectable", "ɵIMAGE_CONFIG", "_IMAGE_CONFIG", "Renderer2", "ElementRef", "Injector", "DestroyRef", "ɵperformanceMarkFeature", "_performanceMarkFeature", "NgZone", "ApplicationRef", "booleanAttribute", "numberAttribute", "ChangeDetectorRef", "ɵIMAGE_CONFIG_DEFAULTS", "_IMAGE_CONFIG_DEFAULTS", "ɵunwrapSafeValue", "_unwrapSafeValue", "Input", "Directive", "IMAGE_CONFIG", "isPlatformBrowser", "XhrFactory", "isPlatformServer", "PLATFORM_BROWSER_ID", "ɵPLATFORM_BROWSER_ID", "PLATFORM_SERVER_ID", "ɵPLATFORM_SERVER_ID", "parseCookieValue", "ɵparseCookieValue", "DOCUMENT", "APP_BASE_HREF", "BrowserPlatformLocation", "LOCATION_INITIALIZED", "Location", "LocationStrategy", "PathLocationStrategy", "PlatformLocation", "DomAdapter", "ɵDomAdapter", "getDOM", "ɵgetDOM", "normalizeQueryParams", "ɵnormalizeQueryParams", "setRootDomAdapter", "ɵsetRootDomAdapter", "PlatformNavigation", "ɵPlatformNavigation", "registerLocaleData", "data", "localeId", "extraData", "VERSION", "ViewportScroller", "ɵprov", "token", "providedIn", "factory", "ngServerMode", "NullViewportScroller", "BrowserViewportScroller", "window", "document", "offset", "constructor", "setOffset", "Array", "isArray", "getScrollPosition", "scrollX", "scrollY", "scrollToPosition", "position", "scrollTo", "scrollToAnchor", "target", "elSelected", "findAnchorFromDocument", "scrollToElement", "focus", "setHistoryScrollRestoration", "scrollRestoration", "history", "el", "rect", "getBoundingClientRect", "left", "pageXOffset", "top", "pageYOffset", "documentResult", "getElementById", "getElementsByName", "createTreeWalker", "body", "attachShadow", "<PERSON><PERSON><PERSON><PERSON>", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "currentNode", "shadowRoot", "result", "querySelector", "nextNode", "anchor", "PLACEHOLDER_QUALITY", "getUrl", "src", "win", "isAbsoluteUrl", "URL", "location", "href", "test", "extractHostname", "url", "hostname", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "isString", "trim", "normalizePath", "endsWith", "slice", "normalizeSrc", "startsWith", "noopImage<PERSON><PERSON>der", "config", "IMAGE_LOADER", "ngDevMode", "createImageLoader", "buildUrlFn", "exampleUrls", "provideImageLoader", "throwInvalidPathError", "loaderFn", "throwUnexpectedAbsoluteUrlError", "providers", "provide", "useValue", "join", "provideCloudflareLoader", "createCloudflareUrl", "undefined", "params", "width", "isPlaceholder", "cloudinaryLoaderInfo", "name", "testUrl", "isCloudinaryUrl", "CLOUDINARY_LOADER_REGEX", "provideCloudinaryLoader", "createCloudinaryUrl", "quality", "loaderParams", "imageKitLoaderInfo", "isImageKitUrl", "IMAGE_KIT_LOADER_REGEX", "provideImageKitLoader", "createImagekitUrl", "push", "urlSegments", "length", "imgixLoaderInfo", "isImgixUrl", "IMGIX_LOADER_REGEX", "provideImgixLoader", "createImgixUrl", "searchParams", "set", "toString", "netlifyLoaderInfo", "isNetlifyUrl", "NETLIFY_LOADER_REGEX", "provideNetlifyLoader", "origin", "createNetlifyUrl", "validParams", "Map", "pathname", "configQuality", "param", "value", "Object", "entries", "has", "get", "console", "warn", "replace", "imgDirectiveDetails", "ngSrc", "includeNgSrc", "ngSrcInfo", "assertDevMode", "checkName", "LCPImageObserver", "images", "observer", "<PERSON><PERSON><PERSON><PERSON>", "defaultView", "PerformanceObserver", "initPerformanceObserver", "entryList", "getEntries", "lcpElement", "imgSrc", "element", "img", "priority", "alreadyWarnedPriority", "logMissingPriorityError", "modified", "alreadyWarnedModified", "logModifiedWarning", "observe", "type", "buffered", "registerImage", "rewrittenSrc", "originalNgSrc", "isPriority", "newObservedImageState", "unregisterImage", "delete", "updateImage", "originalSrc", "newSrc", "originalUrl", "ngOnDestroy", "disconnect", "clear", "ɵfac", "LCPImageObserver_Factory", "__ngFactoryType__", "ɵsetClassMetadata", "args", "directiveDetails", "error", "INTERNAL_PRECONNECT_CHECK_BLOCKLIST", "Set", "PRECONNECT_CHECK_BLOCKLIST", "PreconnectLinkChecker", "preconnectLinks", "alreadySeen", "blocklist", "optional", "populateBlocklist", "origins", "deepForEach", "add", "assertPreconnect", "imgUrl", "queryPreconnectLinks", "preconnectUrls", "links", "querySelectorAll", "link", "PreconnectLinkChecker_Factory", "input", "fn", "DEFAULT_PRELOADED_IMAGES_LIMIT", "PRELOADED_IMAGES", "PreloadLinkCreator", "preloadedImages", "errorShown", "createPreloadLinkTag", "renderer", "srcset", "sizes", "size", "preload", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "head", "PreloadLinkCreator_Factory", "BASE64_IMG_MAX_LENGTH_IN_ERROR", "VALID_WIDTH_DESCRIPTOR_SRCSET", "VALID_DENSITY_DESCRIPTOR_SRCSET", "ABSOLUTE_SRCSET_DENSITY_CAP", "RECOMMENDED_SRCSET_DENSITY_CAP", "DENSITY_SRCSET_MULTIPLIERS", "VIEWPORT_BREAKPOINT_CUTOFF", "ASPECT_RATIO_TOLERANCE", "OVERSIZED_IMAGE_TOLERANCE", "FIXED_SRCSET_WIDTH_LIMIT", "FIXED_SRCSET_HEIGHT_LIMIT", "PLACEHOLDER_BLUR_AMOUNT", "PLACEHOLDER_DIMENSION_LIMIT", "DATA_URL_WARN_LIMIT", "DATA_URL_ERROR_LIMIT", "BUILT_IN_LOADERS", "PRIORITY_COUNT_THRESHOLD", "IMGS_WITH_PRIORITY_ATTR_COUNT", "NgOptimizedImage", "imageLoader", "processConfig", "imgElement", "nativeElement", "injector", "lcpObserver", "_renderedSrc", "ngSrcset", "height", "loading", "disableOptimizedSrcset", "fill", "placeholder", "placeholder<PERSON><PERSON><PERSON>g", "destroyRef", "onDestroy", "ngOnInit", "ngZone", "assertNonEmptyInput", "assertValidNgSrcset", "assertNoConflictingSrc", "assertNoConflictingSrcset", "assertNotBase64Image", "assertNotBlobUrl", "assertEmptyWidthAndHeight", "runOutsideAngular", "assertNonZeroRenderedHeight", "assertNonEmptyWidthAndHeight", "assertGreaterThanZero", "assertNoImageDistortion", "assertValidLoadingInput", "assertNoComplexSizes", "assertValidPlaceholder", "assertNotMissingBuiltInLoader", "assertNoNgSrcsetWithoutLoader", "assertNoLoaderParamsWithoutLoader", "getRewrittenSrc", "checker", "applicationRef", "assetPriorityCountBelowThreshold", "removePlaceholderOnLoad", "setHostAttributes", "setHostAttribute", "getLoading<PERSON><PERSON><PERSON>or", "getFetchPriority", "rewrittenSrcset", "updateSrcAndSrcset", "preloadLinkCreator", "ngOnChanges", "changes", "assertNoPostInitInputChange", "isFirstChange", "oldSrc", "currentValue", "assertPlaceholderDimensions", "callImageLoader", "configWithoutCustomParams", "augmentedConfig", "imgConfig", "getRewrittenSrcset", "widthSrcSet", "finalSrcs", "split", "filter", "map", "srcStr", "parseFloat", "getAutomaticSrcset", "getResponsiveSrcset", "getFixedSrcset", "breakpoints", "filteredBreakpoints", "bp", "forceSrcRecalc", "shouldGenerateAutomaticSrcset", "multiplier", "oversizedImage", "generatePlaceholder", "placeholderInput", "placeholderResolution", "shouldBlurPlaceholder", "hasOwnProperty", "Boolean", "blur", "callback", "changeDetectorRef", "removeLoadListenerFn", "removeErrorListenerFn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listen", "callOnLoadIfImageIsLoaded", "NgOptimizedImage_Factory", "ɵdir", "ɵɵdefineDirective", "selectors", "hostVars", "hostBindings", "NgOptimizedImage_HostBindings", "rf", "ctx", "ɵɵstyleProp", "inputs", "unwrapSafeUrl", "booleanOrUrlAttribute", "features", "ɵɵNgOnChangesFeature", "selector", "host", "required", "transform", "sortedBreakpoints", "sort", "a", "b", "assign", "dir", "substring", "match", "assertNoPlaceholderConfigWithoutPlaceholder", "assertNoRelativePlaceholderWithoutLoader", "assertNoOversizedDataUrl", "isEmptyString", "stringVal", "isValidWidthDescriptor", "isValidDensityDescriptor", "assertUnderDensityCap", "isValidSrcset", "underDensityCap", "every", "num", "postInitInputChangeError", "inputName", "reason", "for<PERSON>ach", "isUpdated", "previousValue", "inputValue", "validNumber", "validString", "parseInt", "computedStyle", "getComputedStyle", "rendered<PERSON><PERSON><PERSON>", "getPropertyValue", "renderedHeight", "boxSizing", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "renderedAspectRatio", "nonZeroRenderedDimensions", "intrinsicWidth", "naturalWidth", "intrinsicHeight", "naturalHeight", "intrinsicAspectRatio", "suppliedWidth", "suppliedHeight", "suppliedAspectRatio", "inaccurateDimensions", "Math", "abs", "stylingDistortion", "round", "recommendedWidth", "recommendedHeight", "oversized<PERSON><PERSON><PERSON>", "oversizedHeight", "missingAttributes", "attr", "clientHeight", "validInputs", "includes", "builtInLoaderName", "loader", "_x", "_assetPriorityCountBelowThreshold", "apply", "arguments", "_asyncToGenerator", "appRef", "whenStable", "complete", "Number", "isInteger", "toFixed", "ɵNullViewportScroller"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/@angular/common/fesm2022/common.mjs"], "sourcesContent": ["/**\n * @license Angular v19.2.14\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nexport { AsyncPipe, CommonModule, CurrencyPipe, DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe, DecimalPipe, FormStyle, FormatWidth, HashLocationStrategy, I18nPluralPipe, I18nSelectPipe, JsonPipe, KeyValuePipe, LowerCasePipe, NgClass, NgComponentOutlet, NgForOf as NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgLocaleLocalization, NgLocalization, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NumberFormatStyle, NumberSymbol, PercentPipe, Plural, SlicePipe, TitleCasePipe, TranslationWidth, UpperCasePipe, WeekDay, formatCurrency, formatDate, formatNumber, formatPercent, getCurrencySymbol, getLocaleCurrencyCode, getLocaleCurrencyName, getLocaleCurrencySymbol, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleDayNames, getLocaleDayPeriods, getLocaleDirection, getLocaleEraNames, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocaleFirstDayOfWeek, getLocaleId, getLocaleMonthNames, getLocaleNumberFormat, getLocaleNumberSymbol, getLocalePluralCase, getLocaleTimeFormat, getLocaleWeekEndRange, getNumberOfCurrencyDigits } from './common_module-Dx7dWex5.mjs';\nimport * as i0 from '@angular/core';\nimport { ɵregisterLocaleData as _registerLocaleData, Version, ɵɵdefineInjectable as __defineInjectable, inject, InjectionToken, ɵRuntimeError as _RuntimeError, ɵformatRuntimeError as _formatRuntimeError, PLATFORM_ID, Injectable, ɵIMAGE_CONFIG as _IMAGE_CONFIG, Renderer2, ElementRef, Injector, DestroyRef, ɵperformanceMarkFeature as _performanceMarkFeature, NgZone, ApplicationRef, booleanAttribute, numberAttribute, ChangeDetectorRef, ɵIMAGE_CONFIG_DEFAULTS as _IMAGE_CONFIG_DEFAULTS, ɵunwrapSafeValue as _unwrapSafeValue, Input, Directive } from '@angular/core';\nexport { ɵIMAGE_CONFIG as IMAGE_CONFIG } from '@angular/core';\nimport { isPlatformBrowser } from './xhr-BfNfxNDv.mjs';\nexport { XhrFactory, isPlatformServer, PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, parseCookieValue as ɵparseCookieValue } from './xhr-BfNfxNDv.mjs';\nimport { DOCUMENT } from './dom_tokens-rA0ACyx7.mjs';\nexport { APP_BASE_HREF, BrowserPlatformLocation, LOCATION_INITIALIZED, Location, LocationStrategy, PathLocationStrategy, PlatformLocation, DomAdapter as ɵDomAdapter, getDOM as ɵgetDOM, normalizeQueryParams as ɵnormalizeQueryParams, setRootDomAdapter as ɵsetRootDomAdapter } from './location-Dq4mJT-A.mjs';\nexport { PlatformNavigation as ɵPlatformNavigation } from './platform_navigation-B45Jeakb.mjs';\nimport 'rxjs';\n\n/**\n * Register global data to be used internally by Angular. See the\n * [\"I18n guide\"](guide/i18n/format-data-locale) to know how to import additional locale\n * data.\n *\n * The signature registerLocaleData(data: any, extraData?: any) is deprecated since v5.1\n *\n * @publicApi\n */\nfunction registerLocaleData(data, localeId, extraData) {\n    return _registerLocaleData(data, localeId, extraData);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('19.2.14');\n\n/**\n * Defines a scroll position manager. Implemented by `BrowserViewportScroller`.\n *\n * @publicApi\n */\nclass ViewportScroller {\n    // De-sugared tree-shakable injection\n    // See #23917\n    /** @nocollapse */\n    static ɵprov = /** @pureOrBreakMyCode */ /* @__PURE__ */ __defineInjectable({\n        token: ViewportScroller,\n        providedIn: 'root',\n        factory: () => typeof ngServerMode !== 'undefined' && ngServerMode\n            ? new NullViewportScroller()\n            : new BrowserViewportScroller(inject(DOCUMENT), window),\n    });\n}\n/**\n * Manages the scroll position for a browser window.\n */\nclass BrowserViewportScroller {\n    document;\n    window;\n    offset = () => [0, 0];\n    constructor(document, window) {\n        this.document = document;\n        this.window = window;\n    }\n    /**\n     * Configures the top offset used when scrolling to an anchor.\n     * @param offset A position in screen coordinates (a tuple with x and y values)\n     * or a function that returns the top offset position.\n     *\n     */\n    setOffset(offset) {\n        if (Array.isArray(offset)) {\n            this.offset = () => offset;\n        }\n        else {\n            this.offset = offset;\n        }\n    }\n    /**\n     * Retrieves the current scroll position.\n     * @returns The position in screen coordinates.\n     */\n    getScrollPosition() {\n        return [this.window.scrollX, this.window.scrollY];\n    }\n    /**\n     * Sets the scroll position.\n     * @param position The new position in screen coordinates.\n     */\n    scrollToPosition(position) {\n        this.window.scrollTo(position[0], position[1]);\n    }\n    /**\n     * Scrolls to an element and attempts to focus the element.\n     *\n     * Note that the function name here is misleading in that the target string may be an ID for a\n     * non-anchor element.\n     *\n     * @param target The ID of an element or name of the anchor.\n     *\n     * @see https://html.spec.whatwg.org/#the-indicated-part-of-the-document\n     * @see https://html.spec.whatwg.org/#scroll-to-fragid\n     */\n    scrollToAnchor(target) {\n        const elSelected = findAnchorFromDocument(this.document, target);\n        if (elSelected) {\n            this.scrollToElement(elSelected);\n            // After scrolling to the element, the spec dictates that we follow the focus steps for the\n            // target. Rather than following the robust steps, simply attempt focus.\n            //\n            // @see https://html.spec.whatwg.org/#get-the-focusable-area\n            // @see https://developer.mozilla.org/en-US/docs/Web/API/HTMLOrForeignElement/focus\n            // @see https://html.spec.whatwg.org/#focusable-area\n            elSelected.focus();\n        }\n    }\n    /**\n     * Disables automatic scroll restoration provided by the browser.\n     */\n    setHistoryScrollRestoration(scrollRestoration) {\n        this.window.history.scrollRestoration = scrollRestoration;\n    }\n    /**\n     * Scrolls to an element using the native offset and the specified offset set on this scroller.\n     *\n     * The offset can be used when we know that there is a floating header and scrolling naively to an\n     * element (ex: `scrollIntoView`) leaves the element hidden behind the floating header.\n     */\n    scrollToElement(el) {\n        const rect = el.getBoundingClientRect();\n        const left = rect.left + this.window.pageXOffset;\n        const top = rect.top + this.window.pageYOffset;\n        const offset = this.offset();\n        this.window.scrollTo(left - offset[0], top - offset[1]);\n    }\n}\nfunction findAnchorFromDocument(document, target) {\n    const documentResult = document.getElementById(target) || document.getElementsByName(target)[0];\n    if (documentResult) {\n        return documentResult;\n    }\n    // `getElementById` and `getElementsByName` won't pierce through the shadow DOM so we\n    // have to traverse the DOM manually and do the lookup through the shadow roots.\n    if (typeof document.createTreeWalker === 'function' &&\n        document.body &&\n        typeof document.body.attachShadow === 'function') {\n        const treeWalker = document.createTreeWalker(document.body, NodeFilter.SHOW_ELEMENT);\n        let currentNode = treeWalker.currentNode;\n        while (currentNode) {\n            const shadowRoot = currentNode.shadowRoot;\n            if (shadowRoot) {\n                // Note that `ShadowRoot` doesn't support `getElementsByName`\n                // so we have to fall back to `querySelector`.\n                const result = shadowRoot.getElementById(target) || shadowRoot.querySelector(`[name=\"${target}\"]`);\n                if (result) {\n                    return result;\n                }\n            }\n            currentNode = treeWalker.nextNode();\n        }\n    }\n    return null;\n}\n/**\n * Provides an empty implementation of the viewport scroller.\n */\nclass NullViewportScroller {\n    /**\n     * Empty implementation\n     */\n    setOffset(offset) { }\n    /**\n     * Empty implementation\n     */\n    getScrollPosition() {\n        return [0, 0];\n    }\n    /**\n     * Empty implementation\n     */\n    scrollToPosition(position) { }\n    /**\n     * Empty implementation\n     */\n    scrollToAnchor(anchor) { }\n    /**\n     * Empty implementation\n     */\n    setHistoryScrollRestoration(scrollRestoration) { }\n}\n\n/**\n * Value (out of 100) of the requested quality for placeholder images.\n */\nconst PLACEHOLDER_QUALITY = '20';\n\n// Converts a string that represents a URL into a URL class instance.\nfunction getUrl(src, win) {\n    // Don't use a base URL is the URL is absolute.\n    return isAbsoluteUrl(src) ? new URL(src) : new URL(src, win.location.href);\n}\n// Checks whether a URL is absolute (i.e. starts with `http://` or `https://`).\nfunction isAbsoluteUrl(src) {\n    return /^https?:\\/\\//.test(src);\n}\n// Given a URL, extract the hostname part.\n// If a URL is a relative one - the URL is returned as is.\nfunction extractHostname(url) {\n    return isAbsoluteUrl(url) ? new URL(url).hostname : url;\n}\nfunction isValidPath(path) {\n    const isString = typeof path === 'string';\n    if (!isString || path.trim() === '') {\n        return false;\n    }\n    // Calling new URL() will throw if the path string is malformed\n    try {\n        const url = new URL(path);\n        return true;\n    }\n    catch {\n        return false;\n    }\n}\nfunction normalizePath(path) {\n    return path.endsWith('/') ? path.slice(0, -1) : path;\n}\nfunction normalizeSrc(src) {\n    return src.startsWith('/') ? src.slice(1) : src;\n}\n\n/**\n * Noop image loader that does no transformation to the original src and just returns it as is.\n * This loader is used as a default one if more specific logic is not provided in an app config.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n */\nconst noopImageLoader = (config) => config.src;\n/**\n * Injection token that configures the image loader function.\n *\n * @see {@link ImageLoader}\n * @see {@link NgOptimizedImage}\n * @publicApi\n */\nconst IMAGE_LOADER = new InjectionToken(ngDevMode ? 'ImageLoader' : '', {\n    providedIn: 'root',\n    factory: () => noopImageLoader,\n});\n/**\n * Internal helper function that makes it easier to introduce custom image loaders for the\n * `NgOptimizedImage` directive. It is enough to specify a URL builder function to obtain full DI\n * configuration for a given loader: a DI token corresponding to the actual loader function, plus DI\n * tokens managing preconnect check functionality.\n * @param buildUrlFn a function returning a full URL based on loader's configuration\n * @param exampleUrls example of full URLs for a given loader (used in error messages)\n * @returns a set of DI providers corresponding to the configured image loader\n */\nfunction createImageLoader(buildUrlFn, exampleUrls) {\n    return function provideImageLoader(path) {\n        if (!isValidPath(path)) {\n            throwInvalidPathError(path, exampleUrls || []);\n        }\n        // The trailing / is stripped (if provided) to make URL construction (concatenation) easier in\n        // the individual loader functions.\n        path = normalizePath(path);\n        const loaderFn = (config) => {\n            if (isAbsoluteUrl(config.src)) {\n                // Image loader functions expect an image file name (e.g. `my-image.png`)\n                // or a relative path + a file name (e.g. `/a/b/c/my-image.png`) as an input,\n                // so the final absolute URL can be constructed.\n                // When an absolute URL is provided instead - the loader can not\n                // build a final URL, thus the error is thrown to indicate that.\n                throwUnexpectedAbsoluteUrlError(path, config.src);\n            }\n            return buildUrlFn(path, { ...config, src: normalizeSrc(config.src) });\n        };\n        const providers = [{ provide: IMAGE_LOADER, useValue: loaderFn }];\n        return providers;\n    };\n}\nfunction throwInvalidPathError(path, exampleUrls) {\n    throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode &&\n        `Image loader has detected an invalid path (\\`${path}\\`). ` +\n            `To fix this, supply a path using one of the following formats: ${exampleUrls.join(' or ')}`);\n}\nfunction throwUnexpectedAbsoluteUrlError(path, url) {\n    throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode &&\n        `Image loader has detected a \\`<img>\\` tag with an invalid \\`ngSrc\\` attribute: ${url}. ` +\n            `This image loader expects \\`ngSrc\\` to be a relative URL - ` +\n            `however the provided value is an absolute URL. ` +\n            `To fix this, provide \\`ngSrc\\` as a path relative to the base URL ` +\n            `configured for this loader (\\`${path}\\`).`);\n}\n\n/**\n * Function that generates an ImageLoader for [Cloudflare Image\n * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular\n * provider. Note: Cloudflare has multiple image products - this provider is specifically for\n * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.\n *\n * @param path Your domain name, e.g. https://mysite.com\n * @returns Provider that provides an ImageLoader function\n *\n * @publicApi\n */\nconst provideCloudflareLoader = createImageLoader(createCloudflareUrl, ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined);\nfunction createCloudflareUrl(path, config) {\n    let params = `format=auto`;\n    if (config.width) {\n        params += `,width=${config.width}`;\n    }\n    // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n    if (config.isPlaceholder) {\n        params += `,quality=${PLACEHOLDER_QUALITY}`;\n    }\n    // Cloudflare image URLs format:\n    // https://developers.cloudflare.com/images/image-resizing/url-format/\n    return `${path}/cdn-cgi/image/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for Cloudinary.\n */\nconst cloudinaryLoaderInfo = {\n    name: 'Cloudinary',\n    testUrl: isCloudinaryUrl,\n};\nconst CLOUDINARY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.cloudinary\\.com\\/.+/;\n/**\n * Tests whether a URL is from Cloudinary CDN.\n */\nfunction isCloudinaryUrl(url) {\n    return CLOUDINARY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Cloudinary and turns it into an Angular provider.\n *\n * @param path Base URL of your Cloudinary images\n * This URL should match one of the following formats:\n * https://res.cloudinary.com/mysite\n * https://mysite.cloudinary.com\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the Cloudinary loader.\n *\n * @publicApi\n */\nconst provideCloudinaryLoader = createImageLoader(createCloudinaryUrl, ngDevMode\n    ? [\n        'https://res.cloudinary.com/mysite',\n        'https://mysite.cloudinary.com',\n        'https://subdomain.mysite.com',\n    ]\n    : undefined);\nfunction createCloudinaryUrl(path, config) {\n    // Cloudinary image URLformat:\n    // https://cloudinary.com/documentation/image_transformations#transformation_url_structure\n    // Example of a Cloudinary image URL:\n    // https://res.cloudinary.com/mysite/image/upload/c_scale,f_auto,q_auto,w_600/marketing/tile-topics-m.png\n    // For a placeholder image, we use the lowest image setting available to reduce the load time\n    // else we use the auto size\n    const quality = config.isPlaceholder ? 'q_auto:low' : 'q_auto';\n    let params = `f_auto,${quality}`;\n    if (config.width) {\n        params += `,w_${config.width}`;\n    }\n    if (config.loaderParams?.['rounded']) {\n        params += `,r_max`;\n    }\n    return `${path}/image/upload/${params}/${config.src}`;\n}\n\n/**\n * Name and URL tester for ImageKit.\n */\nconst imageKitLoaderInfo = {\n    name: 'ImageKit',\n    testUrl: isImageKitUrl,\n};\nconst IMAGE_KIT_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imagekit\\.io\\/.+/;\n/**\n * Tests whether a URL is from ImageKit CDN.\n */\nfunction isImageKitUrl(url) {\n    return IMAGE_KIT_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for ImageKit and turns it into an Angular provider.\n *\n * @param path Base URL of your ImageKit images\n * This URL should match one of the following formats:\n * https://ik.imagekit.io/myaccount\n * https://subdomain.mysite.com\n * @returns Set of providers to configure the ImageKit loader.\n *\n * @publicApi\n */\nconst provideImageKitLoader = createImageLoader(createImagekitUrl, ngDevMode ? ['https://ik.imagekit.io/mysite', 'https://subdomain.mysite.com'] : undefined);\nfunction createImagekitUrl(path, config) {\n    // Example of an ImageKit image URL:\n    // https://ik.imagekit.io/demo/tr:w-300,h-300/medium_cafe_B1iTdD0C.jpg\n    const { src, width } = config;\n    const params = [];\n    if (width) {\n        params.push(`w-${width}`);\n    }\n    // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n    if (config.isPlaceholder) {\n        params.push(`q-${PLACEHOLDER_QUALITY}`);\n    }\n    const urlSegments = params.length ? [path, `tr:${params.join(',')}`, src] : [path, src];\n    const url = new URL(urlSegments.join('/'));\n    return url.href;\n}\n\n/**\n * Name and URL tester for Imgix.\n */\nconst imgixLoaderInfo = {\n    name: 'Imgix',\n    testUrl: isImgixUrl,\n};\nconst IMGIX_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.imgix\\.net\\/.+/;\n/**\n * Tests whether a URL is from Imgix CDN.\n */\nfunction isImgixUrl(url) {\n    return IMGIX_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Imgix and turns it into an Angular provider.\n *\n * @param path path to the desired Imgix origin,\n * e.g. https://somepath.imgix.net or https://images.mysite.com\n * @returns Set of providers to configure the Imgix loader.\n *\n * @publicApi\n */\nconst provideImgixLoader = createImageLoader(createImgixUrl, ngDevMode ? ['https://somepath.imgix.net/'] : undefined);\nfunction createImgixUrl(path, config) {\n    const url = new URL(`${path}/${config.src}`);\n    // This setting ensures the smallest allowable format is set.\n    url.searchParams.set('auto', 'format');\n    if (config.width) {\n        url.searchParams.set('w', config.width.toString());\n    }\n    // When requesting a placeholder image we ask a low quality image to reduce the load time.\n    if (config.isPlaceholder) {\n        url.searchParams.set('q', PLACEHOLDER_QUALITY);\n    }\n    return url.href;\n}\n\n/**\n * Name and URL tester for Netlify.\n */\nconst netlifyLoaderInfo = {\n    name: 'Netlify',\n    testUrl: isNetlifyUrl,\n};\nconst NETLIFY_LOADER_REGEX = /https?\\:\\/\\/[^\\/]+\\.netlify\\.app\\/.+/;\n/**\n * Tests whether a URL is from a Netlify site. This won't catch sites with a custom domain,\n * but it's a good start for sites in development. This is only used to warn users who haven't\n * configured an image loader.\n */\nfunction isNetlifyUrl(url) {\n    return NETLIFY_LOADER_REGEX.test(url);\n}\n/**\n * Function that generates an ImageLoader for Netlify and turns it into an Angular provider.\n *\n * @param path optional URL of the desired Netlify site. Defaults to the current site.\n * @returns Set of providers to configure the Netlify loader.\n *\n * @publicApi\n */\nfunction provideNetlifyLoader(path) {\n    if (path && !isValidPath(path)) {\n        throw new _RuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, ngDevMode &&\n            `Image loader has detected an invalid path (\\`${path}\\`). ` +\n                `To fix this, supply either the full URL to the Netlify site, or leave it empty to use the current site.`);\n    }\n    if (path) {\n        const url = new URL(path);\n        path = url.origin;\n    }\n    const loaderFn = (config) => {\n        return createNetlifyUrl(config, path);\n    };\n    const providers = [{ provide: IMAGE_LOADER, useValue: loaderFn }];\n    return providers;\n}\nconst validParams = new Map([\n    ['height', 'h'],\n    ['fit', 'fit'],\n    ['quality', 'q'],\n    ['q', 'q'],\n    ['position', 'position'],\n]);\nfunction createNetlifyUrl(config, path) {\n    // Note: `path` can be undefined, in which case we use a fake one to construct a `URL` instance.\n    const url = new URL(path ?? 'https://a/');\n    url.pathname = '/.netlify/images';\n    if (!isAbsoluteUrl(config.src) && !config.src.startsWith('/')) {\n        config.src = '/' + config.src;\n    }\n    url.searchParams.set('url', config.src);\n    if (config.width) {\n        url.searchParams.set('w', config.width.toString());\n    }\n    // When requesting a placeholder image we ask for a low quality image to reduce the load time.\n    // If the quality is specified in the loader config - always use provided value.\n    const configQuality = config.loaderParams?.['quality'] ?? config.loaderParams?.['q'];\n    if (config.isPlaceholder && !configQuality) {\n        url.searchParams.set('q', PLACEHOLDER_QUALITY);\n    }\n    for (const [param, value] of Object.entries(config.loaderParams ?? {})) {\n        if (validParams.has(param)) {\n            url.searchParams.set(validParams.get(param), value.toString());\n        }\n        else {\n            if (ngDevMode) {\n                console.warn(_formatRuntimeError(2959 /* RuntimeErrorCode.INVALID_LOADER_ARGUMENTS */, `The Netlify image loader has detected an \\`<img>\\` tag with the unsupported attribute \"\\`${param}\\`\".`));\n            }\n        }\n    }\n    // The \"a\" hostname is used for relative URLs, so we can remove it from the final URL.\n    return url.hostname === 'a' ? url.href.replace(url.origin, '') : url.href;\n}\n\n// Assembles directive details string, useful for error messages.\nfunction imgDirectiveDetails(ngSrc, includeNgSrc = true) {\n    const ngSrcInfo = includeNgSrc\n        ? `(activated on an <img> element with the \\`ngSrc=\"${ngSrc}\"\\`) `\n        : '';\n    return `The NgOptimizedImage directive ${ngSrcInfo}has detected that`;\n}\n\n/**\n * Asserts that the application is in development mode. Throws an error if the application is in\n * production mode. This assert can be used to make sure that there is no dev-mode code invoked in\n * the prod mode accidentally.\n */\nfunction assertDevMode(checkName) {\n    if (!ngDevMode) {\n        throw new _RuntimeError(2958 /* RuntimeErrorCode.UNEXPECTED_DEV_MODE_CHECK_IN_PROD_MODE */, `Unexpected invocation of the ${checkName} in the prod mode. ` +\n            `Please make sure that the prod mode is enabled for production builds.`);\n    }\n}\n\n/**\n * Observer that detects whether an image with `NgOptimizedImage`\n * is treated as a Largest Contentful Paint (LCP) element. If so,\n * asserts that the image has the `priority` attribute.\n *\n * Note: this is a dev-mode only class and it does not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n *\n * Based on https://web.dev/lcp/#measure-lcp-in-javascript.\n */\nclass LCPImageObserver {\n    // Map of full image URLs -> original `ngSrc` values.\n    images = new Map();\n    window = null;\n    observer = null;\n    constructor() {\n        const isBrowser = isPlatformBrowser(inject(PLATFORM_ID));\n        assertDevMode('LCP checker');\n        const win = inject(DOCUMENT).defaultView;\n        if (isBrowser && typeof PerformanceObserver !== 'undefined') {\n            this.window = win;\n            this.observer = this.initPerformanceObserver();\n        }\n    }\n    /**\n     * Inits PerformanceObserver and subscribes to LCP events.\n     * Based on https://web.dev/lcp/#measure-lcp-in-javascript\n     */\n    initPerformanceObserver() {\n        const observer = new PerformanceObserver((entryList) => {\n            const entries = entryList.getEntries();\n            if (entries.length === 0)\n                return;\n            // We use the latest entry produced by the `PerformanceObserver` as the best\n            // signal on which element is actually an LCP one. As an example, the first image to load on\n            // a page, by virtue of being the only thing on the page so far, is often a LCP candidate\n            // and gets reported by PerformanceObserver, but isn't necessarily the LCP element.\n            const lcpElement = entries[entries.length - 1];\n            // Cast to `any` due to missing `element` on the `LargestContentfulPaint` type of entry.\n            // See https://developer.mozilla.org/en-US/docs/Web/API/LargestContentfulPaint\n            const imgSrc = lcpElement.element?.src ?? '';\n            // Exclude `data:` and `blob:` URLs, since they are not supported by the directive.\n            if (imgSrc.startsWith('data:') || imgSrc.startsWith('blob:'))\n                return;\n            const img = this.images.get(imgSrc);\n            if (!img)\n                return;\n            if (!img.priority && !img.alreadyWarnedPriority) {\n                img.alreadyWarnedPriority = true;\n                logMissingPriorityError(imgSrc);\n            }\n            if (img.modified && !img.alreadyWarnedModified) {\n                img.alreadyWarnedModified = true;\n                logModifiedWarning(imgSrc);\n            }\n        });\n        observer.observe({ type: 'largest-contentful-paint', buffered: true });\n        return observer;\n    }\n    registerImage(rewrittenSrc, originalNgSrc, isPriority) {\n        if (!this.observer)\n            return;\n        const newObservedImageState = {\n            priority: isPriority,\n            modified: false,\n            alreadyWarnedModified: false,\n            alreadyWarnedPriority: false,\n        };\n        this.images.set(getUrl(rewrittenSrc, this.window).href, newObservedImageState);\n    }\n    unregisterImage(rewrittenSrc) {\n        if (!this.observer)\n            return;\n        this.images.delete(getUrl(rewrittenSrc, this.window).href);\n    }\n    updateImage(originalSrc, newSrc) {\n        if (!this.observer)\n            return;\n        const originalUrl = getUrl(originalSrc, this.window).href;\n        const img = this.images.get(originalUrl);\n        if (img) {\n            img.modified = true;\n            this.images.set(getUrl(newSrc, this.window).href, img);\n            this.images.delete(originalUrl);\n        }\n    }\n    ngOnDestroy() {\n        if (!this.observer)\n            return;\n        this.observer.disconnect();\n        this.images.clear();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LCPImageObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LCPImageObserver, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: LCPImageObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\nfunction logMissingPriorityError(ngSrc) {\n    const directiveDetails = imgDirectiveDetails(ngSrc);\n    console.error(_formatRuntimeError(2955 /* RuntimeErrorCode.LCP_IMG_MISSING_PRIORITY */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element but was not marked \"priority\". This image should be marked ` +\n        `\"priority\" in order to prioritize its loading. ` +\n        `To fix this, add the \"priority\" attribute.`));\n}\nfunction logModifiedWarning(ngSrc) {\n    const directiveDetails = imgDirectiveDetails(ngSrc);\n    console.warn(_formatRuntimeError(2964 /* RuntimeErrorCode.LCP_IMG_NGSRC_MODIFIED */, `${directiveDetails} this image is the Largest Contentful Paint (LCP) ` +\n        `element and has had its \"ngSrc\" attribute modified. This can cause ` +\n        `slower loading performance. It is recommended not to modify the \"ngSrc\" ` +\n        `property on any image which could be the LCP element.`));\n}\n\n// Set of origins that are always excluded from the preconnect checks.\nconst INTERNAL_PRECONNECT_CHECK_BLOCKLIST = new Set(['localhost', '127.0.0.1', '0.0.0.0']);\n/**\n * Injection token to configure which origins should be excluded\n * from the preconnect checks. It can either be a single string or an array of strings\n * to represent a group of origins, for example:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST, useValue: 'https://your-domain.com'}\n * ```\n *\n * or:\n *\n * ```ts\n *  {provide: PRECONNECT_CHECK_BLOCKLIST,\n *   useValue: ['https://your-domain-1.com', 'https://your-domain-2.com']}\n * ```\n *\n * @publicApi\n */\nconst PRECONNECT_CHECK_BLOCKLIST = new InjectionToken(ngDevMode ? 'PRECONNECT_CHECK_BLOCKLIST' : '');\n/**\n * Contains the logic to detect whether an image, marked with the \"priority\" attribute\n * has a corresponding `<link rel=\"preconnect\">` tag in the `document.head`.\n *\n * Note: this is a dev-mode only class, which should not appear in prod bundles,\n * thus there is no `ngDevMode` use in the code.\n */\nclass PreconnectLinkChecker {\n    document = inject(DOCUMENT);\n    /**\n     * Set of <link rel=\"preconnect\"> tags found on this page.\n     * The `null` value indicates that there was no DOM query operation performed.\n     */\n    preconnectLinks = null;\n    /*\n     * Keep track of all already seen origin URLs to avoid repeating the same check.\n     */\n    alreadySeen = new Set();\n    window = this.document.defaultView;\n    blocklist = new Set(INTERNAL_PRECONNECT_CHECK_BLOCKLIST);\n    constructor() {\n        assertDevMode('preconnect link checker');\n        const blocklist = inject(PRECONNECT_CHECK_BLOCKLIST, { optional: true });\n        if (blocklist) {\n            this.populateBlocklist(blocklist);\n        }\n    }\n    populateBlocklist(origins) {\n        if (Array.isArray(origins)) {\n            deepForEach(origins, (origin) => {\n                this.blocklist.add(extractHostname(origin));\n            });\n        }\n        else {\n            this.blocklist.add(extractHostname(origins));\n        }\n    }\n    /**\n     * Checks that a preconnect resource hint exists in the head for the\n     * given src.\n     *\n     * @param rewrittenSrc src formatted with loader\n     * @param originalNgSrc ngSrc value\n     */\n    assertPreconnect(rewrittenSrc, originalNgSrc) {\n        if (typeof ngServerMode !== 'undefined' && ngServerMode)\n            return;\n        const imgUrl = getUrl(rewrittenSrc, this.window);\n        if (this.blocklist.has(imgUrl.hostname) || this.alreadySeen.has(imgUrl.origin))\n            return;\n        // Register this origin as seen, so we don't check it again later.\n        this.alreadySeen.add(imgUrl.origin);\n        // Note: we query for preconnect links only *once* and cache the results\n        // for the entire lifespan of an application, since it's unlikely that the\n        // list would change frequently. This allows to make sure there are no\n        // performance implications of making extra DOM lookups for each image.\n        this.preconnectLinks ??= this.queryPreconnectLinks();\n        if (!this.preconnectLinks.has(imgUrl.origin)) {\n            console.warn(_formatRuntimeError(2956 /* RuntimeErrorCode.PRIORITY_IMG_MISSING_PRECONNECT_TAG */, `${imgDirectiveDetails(originalNgSrc)} there is no preconnect tag present for this ` +\n                `image. Preconnecting to the origin(s) that serve priority images ensures that these ` +\n                `images are delivered as soon as possible. To fix this, please add the following ` +\n                `element into the <head> of the document:\\n` +\n                `  <link rel=\"preconnect\" href=\"${imgUrl.origin}\">`));\n        }\n    }\n    queryPreconnectLinks() {\n        const preconnectUrls = new Set();\n        const links = this.document.querySelectorAll('link[rel=preconnect]');\n        for (const link of links) {\n            const url = getUrl(link.href, this.window);\n            preconnectUrls.add(url.origin);\n        }\n        return preconnectUrls;\n    }\n    ngOnDestroy() {\n        this.preconnectLinks?.clear();\n        this.alreadySeen.clear();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreconnectLinkChecker, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreconnectLinkChecker, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreconnectLinkChecker, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [] });\n/**\n * Invokes a callback for each element in the array. Also invokes a callback\n * recursively for each nested array.\n */\nfunction deepForEach(input, fn) {\n    for (let value of input) {\n        Array.isArray(value) ? deepForEach(value, fn) : fn(value);\n    }\n}\n\n/**\n * In SSR scenarios, a preload `<link>` element is generated for priority images.\n * Having a large number of preload tags may negatively affect the performance,\n * so we warn developers (by throwing an error) if the number of preloaded images\n * is above a certain threshold. This const specifies this threshold.\n */\nconst DEFAULT_PRELOADED_IMAGES_LIMIT = 5;\n/**\n * Helps to keep track of priority images that already have a corresponding\n * preload tag (to avoid generating multiple preload tags with the same URL).\n *\n * This Set tracks the original src passed into the `ngSrc` input not the src after it has been\n * run through the specified `IMAGE_LOADER`.\n */\nconst PRELOADED_IMAGES = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'NG_OPTIMIZED_PRELOADED_IMAGES' : '', {\n    providedIn: 'root',\n    factory: () => new Set(),\n});\n\n/**\n * @description Contains the logic needed to track and add preload link tags to the `<head>` tag. It\n * will also track what images have already had preload link tags added so as to not duplicate link\n * tags.\n *\n * In dev mode this service will validate that the number of preloaded images does not exceed the\n * configured default preloaded images limit: {@link DEFAULT_PRELOADED_IMAGES_LIMIT}.\n */\nclass PreloadLinkCreator {\n    preloadedImages = inject(PRELOADED_IMAGES);\n    document = inject(DOCUMENT);\n    errorShown = false;\n    /**\n     * @description Add a preload `<link>` to the `<head>` of the `index.html` that is served from the\n     * server while using Angular Universal and SSR to kick off image loads for high priority images.\n     *\n     * The `sizes` (passed in from the user) and `srcset` (parsed and formatted from `ngSrcset`)\n     * properties used to set the corresponding attributes, `imagesizes` and `imagesrcset`\n     * respectively, on the preload `<link>` tag so that the correctly sized image is preloaded from\n     * the CDN.\n     *\n     * {@link https://web.dev/preload-responsive-images/#imagesrcset-and-imagesizes}\n     *\n     * @param renderer The `Renderer2` passed in from the directive\n     * @param src The original src of the image that is set on the `ngSrc` input.\n     * @param srcset The parsed and formatted srcset created from the `ngSrcset` input\n     * @param sizes The value of the `sizes` attribute passed in to the `<img>` tag\n     */\n    createPreloadLinkTag(renderer, src, srcset, sizes) {\n        if (ngDevMode &&\n            !this.errorShown &&\n            this.preloadedImages.size >= DEFAULT_PRELOADED_IMAGES_LIMIT) {\n            this.errorShown = true;\n            console.warn(_formatRuntimeError(2961 /* RuntimeErrorCode.TOO_MANY_PRELOADED_IMAGES */, `The \\`NgOptimizedImage\\` directive has detected that more than ` +\n                `${DEFAULT_PRELOADED_IMAGES_LIMIT} images were marked as priority. ` +\n                `This might negatively affect an overall performance of the page. ` +\n                `To fix this, remove the \"priority\" attribute from images with less priority.`));\n        }\n        if (this.preloadedImages.has(src)) {\n            return;\n        }\n        this.preloadedImages.add(src);\n        const preload = renderer.createElement('link');\n        renderer.setAttribute(preload, 'as', 'image');\n        renderer.setAttribute(preload, 'href', src);\n        renderer.setAttribute(preload, 'rel', 'preload');\n        renderer.setAttribute(preload, 'fetchpriority', 'high');\n        if (sizes) {\n            renderer.setAttribute(preload, 'imageSizes', sizes);\n        }\n        if (srcset) {\n            renderer.setAttribute(preload, 'imageSrcset', srcset);\n        }\n        renderer.appendChild(this.document.head, preload);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadLinkCreator, deps: [], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadLinkCreator, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: PreloadLinkCreator, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n\n/**\n * When a Base64-encoded image is passed as an input to the `NgOptimizedImage` directive,\n * an error is thrown. The image content (as a string) might be very long, thus making\n * it hard to read an error message if the entire string is included. This const defines\n * the number of characters that should be included into the error message. The rest\n * of the content is truncated.\n */\nconst BASE64_IMG_MAX_LENGTH_IN_ERROR = 50;\n/**\n * RegExpr to determine whether a src in a srcset is using width descriptors.\n * Should match something like: \"100w, 200w\".\n */\nconst VALID_WIDTH_DESCRIPTOR_SRCSET = /^((\\s*\\d+w\\s*(,|$)){1,})$/;\n/**\n * RegExpr to determine whether a src in a srcset is using density descriptors.\n * Should match something like: \"1x, 2x, 50x\". Also supports decimals like \"1.5x, 1.50x\".\n */\nconst VALID_DENSITY_DESCRIPTOR_SRCSET = /^((\\s*\\d+(\\.\\d+)?x\\s*(,|$)){1,})$/;\n/**\n * Srcset values with a density descriptor higher than this value will actively\n * throw an error. Such densities are not permitted as they cause image sizes\n * to be unreasonably large and slow down LCP.\n */\nconst ABSOLUTE_SRCSET_DENSITY_CAP = 3;\n/**\n * Used only in error message text to communicate best practices, as we will\n * only throw based on the slightly more conservative ABSOLUTE_SRCSET_DENSITY_CAP.\n */\nconst RECOMMENDED_SRCSET_DENSITY_CAP = 2;\n/**\n * Used in generating automatic density-based srcsets\n */\nconst DENSITY_SRCSET_MULTIPLIERS = [1, 2];\n/**\n * Used to determine which breakpoints to use on full-width images\n */\nconst VIEWPORT_BREAKPOINT_CUTOFF = 640;\n/**\n * Used to determine whether two aspect ratios are similar in value.\n */\nconst ASPECT_RATIO_TOLERANCE = 0.1;\n/**\n * Used to determine whether the image has been requested at an overly\n * large size compared to the actual rendered image size (after taking\n * into account a typical device pixel ratio). In pixels.\n */\nconst OVERSIZED_IMAGE_TOLERANCE = 1000;\n/**\n * Used to limit automatic srcset generation of very large sources for\n * fixed-size images. In pixels.\n */\nconst FIXED_SRCSET_WIDTH_LIMIT = 1920;\nconst FIXED_SRCSET_HEIGHT_LIMIT = 1080;\n/**\n * Default blur radius of the CSS filter used on placeholder images, in pixels\n */\nconst PLACEHOLDER_BLUR_AMOUNT = 15;\n/**\n * Placeholder dimension (height or width) limit in pixels. Angular produces a warning\n * when this limit is crossed.\n */\nconst PLACEHOLDER_DIMENSION_LIMIT = 1000;\n/**\n * Used to warn or error when the user provides an overly large dataURL for the placeholder\n * attribute.\n * Character count of Base64 images is 1 character per byte, and base64 encoding is approximately\n * 33% larger than base images, so 4000 characters is around 3KB on disk and 10000 characters is\n * around 7.7KB. Experimentally, 4000 characters is about 20x20px in PNG or medium-quality JPEG\n * format, and 10,000 is around 50x50px, but there's quite a bit of variation depending on how the\n * image is saved.\n */\nconst DATA_URL_WARN_LIMIT = 4000;\nconst DATA_URL_ERROR_LIMIT = 10000;\n/** Info about built-in loaders we can test for. */\nconst BUILT_IN_LOADERS = [\n    imgixLoaderInfo,\n    imageKitLoaderInfo,\n    cloudinaryLoaderInfo,\n    netlifyLoaderInfo,\n];\n/**\n * Threshold for the PRIORITY_TRUE_COUNT\n */\nconst PRIORITY_COUNT_THRESHOLD = 10;\n/**\n * This count is used to log a devMode warning\n * when the count of directive instances with priority=true\n * exceeds the threshold PRIORITY_COUNT_THRESHOLD\n */\nlet IMGS_WITH_PRIORITY_ATTR_COUNT = 0;\n/**\n * Directive that improves image loading performance by enforcing best practices.\n *\n * `NgOptimizedImage` ensures that the loading of the Largest Contentful Paint (LCP) image is\n * prioritized by:\n * - Automatically setting the `fetchpriority` attribute on the `<img>` tag\n * - Lazy loading non-priority images by default\n * - Automatically generating a preconnect link tag in the document head\n *\n * In addition, the directive:\n * - Generates appropriate asset URLs if a corresponding `ImageLoader` function is provided\n * - Automatically generates a srcset\n * - Requires that `width` and `height` are set\n * - Warns if `width` or `height` have been set incorrectly\n * - Warns if the image will be visually distorted when rendered\n *\n * @usageNotes\n * The `NgOptimizedImage` directive is marked as [standalone](guide/components/importing) and can\n * be imported directly.\n *\n * Follow the steps below to enable and use the directive:\n * 1. Import it into the necessary NgModule or a standalone Component.\n * 2. Optionally provide an `ImageLoader` if you use an image hosting service.\n * 3. Update the necessary `<img>` tags in templates and replace `src` attributes with `ngSrc`.\n * Using a `ngSrc` allows the directive to control when the `src` gets set, which triggers an image\n * download.\n *\n * Step 1: import the `NgOptimizedImage` directive.\n *\n * ```ts\n * import { NgOptimizedImage } from '@angular/common';\n *\n * // Include it into the necessary NgModule\n * @NgModule({\n *   imports: [NgOptimizedImage],\n * })\n * class AppModule {}\n *\n * // ... or a standalone Component\n * @Component({\n *   imports: [NgOptimizedImage],\n * })\n * class MyStandaloneComponent {}\n * ```\n *\n * Step 2: configure a loader.\n *\n * To use the **default loader**: no additional code changes are necessary. The URL returned by the\n * generic loader will always match the value of \"src\". In other words, this loader applies no\n * transformations to the resource URL and the value of the `ngSrc` attribute will be used as is.\n *\n * To use an existing loader for a **third-party image service**: add the provider factory for your\n * chosen service to the `providers` array. In the example below, the Imgix loader is used:\n *\n * ```ts\n * import {provideImgixLoader} from '@angular/common';\n *\n * // Call the function and add the result to the `providers` array:\n * providers: [\n *   provideImgixLoader(\"https://my.base.url/\"),\n * ],\n * ```\n *\n * The `NgOptimizedImage` directive provides the following functions:\n * - `provideCloudflareLoader`\n * - `provideCloudinaryLoader`\n * - `provideImageKitLoader`\n * - `provideImgixLoader`\n *\n * If you use a different image provider, you can create a custom loader function as described\n * below.\n *\n * To use a **custom loader**: provide your loader function as a value for the `IMAGE_LOADER` DI\n * token.\n *\n * ```ts\n * import {IMAGE_LOADER, ImageLoaderConfig} from '@angular/common';\n *\n * // Configure the loader using the `IMAGE_LOADER` token.\n * providers: [\n *   {\n *      provide: IMAGE_LOADER,\n *      useValue: (config: ImageLoaderConfig) => {\n *        return `https://example.com/${config.src}-${config.width}.jpg`;\n *      }\n *   },\n * ],\n * ```\n *\n * Step 3: update `<img>` tags in templates to use `ngSrc` instead of `src`.\n *\n * ```html\n * <img ngSrc=\"logo.png\" width=\"200\" height=\"100\">\n * ```\n *\n * @publicApi\n */\nclass NgOptimizedImage {\n    imageLoader = inject(IMAGE_LOADER);\n    config = processConfig(inject(_IMAGE_CONFIG));\n    renderer = inject(Renderer2);\n    imgElement = inject(ElementRef).nativeElement;\n    injector = inject(Injector);\n    // An LCP image observer should be injected only in development mode.\n    // Do not assign it to `null` to avoid having a redundant property in the production bundle.\n    lcpObserver;\n    /**\n     * Calculate the rewritten `src` once and store it.\n     * This is needed to avoid repetitive calculations and make sure the directive cleanup in the\n     * `ngOnDestroy` does not rely on the `IMAGE_LOADER` logic (which in turn can rely on some other\n     * instance that might be already destroyed).\n     */\n    _renderedSrc = null;\n    /**\n     * Name of the source image.\n     * Image name will be processed by the image loader and the final URL will be applied as the `src`\n     * property of the image.\n     */\n    ngSrc;\n    /**\n     * A comma separated list of width or density descriptors.\n     * The image name will be taken from `ngSrc` and combined with the list of width or density\n     * descriptors to generate the final `srcset` property of the image.\n     *\n     * Example:\n     * ```html\n     * <img ngSrc=\"hello.jpg\" ngSrcset=\"100w, 200w\" />  =>\n     * <img src=\"path/hello.jpg\" srcset=\"path/hello.jpg?w=100 100w, path/hello.jpg?w=200 200w\" />\n     * ```\n     */\n    ngSrcset;\n    /**\n     * The base `sizes` attribute passed through to the `<img>` element.\n     * Providing sizes causes the image to create an automatic responsive srcset.\n     */\n    sizes;\n    /**\n     * For responsive images: the intrinsic width of the image in pixels.\n     * For fixed size images: the desired rendered width of the image in pixels.\n     */\n    width;\n    /**\n     * For responsive images: the intrinsic height of the image in pixels.\n     * For fixed size images: the desired rendered height of the image in pixels.\n     */\n    height;\n    /**\n     * The desired loading behavior (lazy, eager, or auto). Defaults to `lazy`,\n     * which is recommended for most images.\n     *\n     * Warning: Setting images as loading=\"eager\" or loading=\"auto\" marks them\n     * as non-priority images and can hurt loading performance. For images which\n     * may be the LCP element, use the `priority` attribute instead of `loading`.\n     */\n    loading;\n    /**\n     * Indicates whether this image should have a high priority.\n     */\n    priority = false;\n    /**\n     * Data to pass through to custom loaders.\n     */\n    loaderParams;\n    /**\n     * Disables automatic srcset generation for this image.\n     */\n    disableOptimizedSrcset = false;\n    /**\n     * Sets the image to \"fill mode\", which eliminates the height/width requirement and adds\n     * styles such that the image fills its containing element.\n     */\n    fill = false;\n    /**\n     * A URL or data URL for an image to be used as a placeholder while this image loads.\n     */\n    placeholder;\n    /**\n     * Configuration object for placeholder settings. Options:\n     *   * blur: Setting this to false disables the automatic CSS blur.\n     */\n    placeholderConfig;\n    /**\n     * Value of the `src` attribute if set on the host `<img>` element.\n     * This input is exclusively read to assert that `src` is not set in conflict\n     * with `ngSrc` and that images don't start to load until a lazy loading strategy is set.\n     * @internal\n     */\n    src;\n    /**\n     * Value of the `srcset` attribute if set on the host `<img>` element.\n     * This input is exclusively read to assert that `srcset` is not set in conflict\n     * with `ngSrcset` and that images don't start to load until a lazy loading strategy is set.\n     * @internal\n     */\n    srcset;\n    constructor() {\n        if (ngDevMode) {\n            this.lcpObserver = this.injector.get(LCPImageObserver);\n            // Using `DestroyRef` to avoid having an empty `ngOnDestroy` method since this\n            // is only run in development mode.\n            const destroyRef = inject(DestroyRef);\n            destroyRef.onDestroy(() => {\n                if (!this.priority && this._renderedSrc !== null) {\n                    this.lcpObserver.unregisterImage(this._renderedSrc);\n                }\n            });\n        }\n    }\n    /** @docs-private */\n    ngOnInit() {\n        _performanceMarkFeature('NgOptimizedImage');\n        if (ngDevMode) {\n            const ngZone = this.injector.get(NgZone);\n            assertNonEmptyInput(this, 'ngSrc', this.ngSrc);\n            assertValidNgSrcset(this, this.ngSrcset);\n            assertNoConflictingSrc(this);\n            if (this.ngSrcset) {\n                assertNoConflictingSrcset(this);\n            }\n            assertNotBase64Image(this);\n            assertNotBlobUrl(this);\n            if (this.fill) {\n                assertEmptyWidthAndHeight(this);\n                // This leaves the Angular zone to avoid triggering unnecessary change detection cycles when\n                // `load` tasks are invoked on images.\n                ngZone.runOutsideAngular(() => assertNonZeroRenderedHeight(this, this.imgElement, this.renderer));\n            }\n            else {\n                assertNonEmptyWidthAndHeight(this);\n                if (this.height !== undefined) {\n                    assertGreaterThanZero(this, this.height, 'height');\n                }\n                if (this.width !== undefined) {\n                    assertGreaterThanZero(this, this.width, 'width');\n                }\n                // Only check for distorted images when not in fill mode, where\n                // images may be intentionally stretched, cropped or letterboxed.\n                ngZone.runOutsideAngular(() => assertNoImageDistortion(this, this.imgElement, this.renderer));\n            }\n            assertValidLoadingInput(this);\n            if (!this.ngSrcset) {\n                assertNoComplexSizes(this);\n            }\n            assertValidPlaceholder(this, this.imageLoader);\n            assertNotMissingBuiltInLoader(this.ngSrc, this.imageLoader);\n            assertNoNgSrcsetWithoutLoader(this, this.imageLoader);\n            assertNoLoaderParamsWithoutLoader(this, this.imageLoader);\n            ngZone.runOutsideAngular(() => {\n                this.lcpObserver.registerImage(this.getRewrittenSrc(), this.ngSrc, this.priority);\n            });\n            if (this.priority) {\n                const checker = this.injector.get(PreconnectLinkChecker);\n                checker.assertPreconnect(this.getRewrittenSrc(), this.ngSrc);\n                if (typeof ngServerMode !== 'undefined' && !ngServerMode) {\n                    const applicationRef = this.injector.get(ApplicationRef);\n                    assetPriorityCountBelowThreshold(applicationRef);\n                }\n            }\n        }\n        if (this.placeholder) {\n            this.removePlaceholderOnLoad(this.imgElement);\n        }\n        this.setHostAttributes();\n    }\n    setHostAttributes() {\n        // Must set width/height explicitly in case they are bound (in which case they will\n        // only be reflected and not found by the browser)\n        if (this.fill) {\n            this.sizes ||= '100vw';\n        }\n        else {\n            this.setHostAttribute('width', this.width.toString());\n            this.setHostAttribute('height', this.height.toString());\n        }\n        this.setHostAttribute('loading', this.getLoadingBehavior());\n        this.setHostAttribute('fetchpriority', this.getFetchPriority());\n        // The `data-ng-img` attribute flags an image as using the directive, to allow\n        // for analysis of the directive's performance.\n        this.setHostAttribute('ng-img', 'true');\n        // The `src` and `srcset` attributes should be set last since other attributes\n        // could affect the image's loading behavior.\n        const rewrittenSrcset = this.updateSrcAndSrcset();\n        if (this.sizes) {\n            if (this.getLoadingBehavior() === 'lazy') {\n                this.setHostAttribute('sizes', 'auto, ' + this.sizes);\n            }\n            else {\n                this.setHostAttribute('sizes', this.sizes);\n            }\n        }\n        else {\n            if (this.ngSrcset &&\n                VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset) &&\n                this.getLoadingBehavior() === 'lazy') {\n                this.setHostAttribute('sizes', 'auto, 100vw');\n            }\n        }\n        if (typeof ngServerMode !== 'undefined' && ngServerMode && this.priority) {\n            const preloadLinkCreator = this.injector.get(PreloadLinkCreator);\n            preloadLinkCreator.createPreloadLinkTag(this.renderer, this.getRewrittenSrc(), rewrittenSrcset, this.sizes);\n        }\n    }\n    /** @docs-private */\n    ngOnChanges(changes) {\n        if (ngDevMode) {\n            assertNoPostInitInputChange(this, changes, [\n                'ngSrcset',\n                'width',\n                'height',\n                'priority',\n                'fill',\n                'loading',\n                'sizes',\n                'loaderParams',\n                'disableOptimizedSrcset',\n            ]);\n        }\n        if (changes['ngSrc'] && !changes['ngSrc'].isFirstChange()) {\n            const oldSrc = this._renderedSrc;\n            this.updateSrcAndSrcset(true);\n            if (ngDevMode) {\n                const newSrc = this._renderedSrc;\n                if (oldSrc && newSrc && oldSrc !== newSrc) {\n                    const ngZone = this.injector.get(NgZone);\n                    ngZone.runOutsideAngular(() => {\n                        this.lcpObserver.updateImage(oldSrc, newSrc);\n                    });\n                }\n            }\n        }\n        if (ngDevMode &&\n            changes['placeholder']?.currentValue &&\n            typeof ngServerMode !== 'undefined' &&\n            !ngServerMode) {\n            assertPlaceholderDimensions(this, this.imgElement);\n        }\n    }\n    callImageLoader(configWithoutCustomParams) {\n        let augmentedConfig = configWithoutCustomParams;\n        if (this.loaderParams) {\n            augmentedConfig.loaderParams = this.loaderParams;\n        }\n        return this.imageLoader(augmentedConfig);\n    }\n    getLoadingBehavior() {\n        if (!this.priority && this.loading !== undefined) {\n            return this.loading;\n        }\n        return this.priority ? 'eager' : 'lazy';\n    }\n    getFetchPriority() {\n        return this.priority ? 'high' : 'auto';\n    }\n    getRewrittenSrc() {\n        // ImageLoaderConfig supports setting a width property. However, we're not setting width here\n        // because if the developer uses rendered width instead of intrinsic width in the HTML width\n        // attribute, the image requested may be too small for 2x+ screens.\n        if (!this._renderedSrc) {\n            const imgConfig = { src: this.ngSrc };\n            // Cache calculated image src to reuse it later in the code.\n            this._renderedSrc = this.callImageLoader(imgConfig);\n        }\n        return this._renderedSrc;\n    }\n    getRewrittenSrcset() {\n        const widthSrcSet = VALID_WIDTH_DESCRIPTOR_SRCSET.test(this.ngSrcset);\n        const finalSrcs = this.ngSrcset\n            .split(',')\n            .filter((src) => src !== '')\n            .map((srcStr) => {\n            srcStr = srcStr.trim();\n            const width = widthSrcSet ? parseFloat(srcStr) : parseFloat(srcStr) * this.width;\n            return `${this.callImageLoader({ src: this.ngSrc, width })} ${srcStr}`;\n        });\n        return finalSrcs.join(', ');\n    }\n    getAutomaticSrcset() {\n        if (this.sizes) {\n            return this.getResponsiveSrcset();\n        }\n        else {\n            return this.getFixedSrcset();\n        }\n    }\n    getResponsiveSrcset() {\n        const { breakpoints } = this.config;\n        let filteredBreakpoints = breakpoints;\n        if (this.sizes?.trim() === '100vw') {\n            // Since this is a full-screen-width image, our srcset only needs to include\n            // breakpoints with full viewport widths.\n            filteredBreakpoints = breakpoints.filter((bp) => bp >= VIEWPORT_BREAKPOINT_CUTOFF);\n        }\n        const finalSrcs = filteredBreakpoints.map((bp) => `${this.callImageLoader({ src: this.ngSrc, width: bp })} ${bp}w`);\n        return finalSrcs.join(', ');\n    }\n    updateSrcAndSrcset(forceSrcRecalc = false) {\n        if (forceSrcRecalc) {\n            // Reset cached value, so that the followup `getRewrittenSrc()` call\n            // will recalculate it and update the cache.\n            this._renderedSrc = null;\n        }\n        const rewrittenSrc = this.getRewrittenSrc();\n        this.setHostAttribute('src', rewrittenSrc);\n        let rewrittenSrcset = undefined;\n        if (this.ngSrcset) {\n            rewrittenSrcset = this.getRewrittenSrcset();\n        }\n        else if (this.shouldGenerateAutomaticSrcset()) {\n            rewrittenSrcset = this.getAutomaticSrcset();\n        }\n        if (rewrittenSrcset) {\n            this.setHostAttribute('srcset', rewrittenSrcset);\n        }\n        return rewrittenSrcset;\n    }\n    getFixedSrcset() {\n        const finalSrcs = DENSITY_SRCSET_MULTIPLIERS.map((multiplier) => `${this.callImageLoader({\n            src: this.ngSrc,\n            width: this.width * multiplier,\n        })} ${multiplier}x`);\n        return finalSrcs.join(', ');\n    }\n    shouldGenerateAutomaticSrcset() {\n        let oversizedImage = false;\n        if (!this.sizes) {\n            oversizedImage =\n                this.width > FIXED_SRCSET_WIDTH_LIMIT || this.height > FIXED_SRCSET_HEIGHT_LIMIT;\n        }\n        return (!this.disableOptimizedSrcset &&\n            !this.srcset &&\n            this.imageLoader !== noopImageLoader &&\n            !oversizedImage);\n    }\n    /**\n     * Returns an image url formatted for use with the CSS background-image property. Expects one of:\n     * * A base64 encoded image, which is wrapped and passed through.\n     * * A boolean. If true, calls the image loader to generate a small placeholder url.\n     */\n    generatePlaceholder(placeholderInput) {\n        const { placeholderResolution } = this.config;\n        if (placeholderInput === true) {\n            return `url(${this.callImageLoader({\n                src: this.ngSrc,\n                width: placeholderResolution,\n                isPlaceholder: true,\n            })})`;\n        }\n        else if (typeof placeholderInput === 'string') {\n            return `url(${placeholderInput})`;\n        }\n        return null;\n    }\n    /**\n     * Determines if blur should be applied, based on an optional boolean\n     * property `blur` within the optional configuration object `placeholderConfig`.\n     */\n    shouldBlurPlaceholder(placeholderConfig) {\n        if (!placeholderConfig || !placeholderConfig.hasOwnProperty('blur')) {\n            return true;\n        }\n        return Boolean(placeholderConfig.blur);\n    }\n    removePlaceholderOnLoad(img) {\n        const callback = () => {\n            const changeDetectorRef = this.injector.get(ChangeDetectorRef);\n            removeLoadListenerFn();\n            removeErrorListenerFn();\n            this.placeholder = false;\n            changeDetectorRef.markForCheck();\n        };\n        const removeLoadListenerFn = this.renderer.listen(img, 'load', callback);\n        const removeErrorListenerFn = this.renderer.listen(img, 'error', callback);\n        callOnLoadIfImageIsLoaded(img, callback);\n    }\n    setHostAttribute(name, value) {\n        this.renderer.setAttribute(this.imgElement, name, value);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NgOptimizedImage, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.14\", type: NgOptimizedImage, isStandalone: true, selector: \"img[ngSrc]\", inputs: { ngSrc: [\"ngSrc\", \"ngSrc\", unwrapSafeUrl], ngSrcset: \"ngSrcset\", sizes: \"sizes\", width: [\"width\", \"width\", numberAttribute], height: [\"height\", \"height\", numberAttribute], loading: \"loading\", priority: [\"priority\", \"priority\", booleanAttribute], loaderParams: \"loaderParams\", disableOptimizedSrcset: [\"disableOptimizedSrcset\", \"disableOptimizedSrcset\", booleanAttribute], fill: [\"fill\", \"fill\", booleanAttribute], placeholder: [\"placeholder\", \"placeholder\", booleanOrUrlAttribute], placeholderConfig: \"placeholderConfig\", src: \"src\", srcset: \"srcset\" }, host: { properties: { \"style.position\": \"fill ? \\\"absolute\\\" : null\", \"style.width\": \"fill ? \\\"100%\\\" : null\", \"style.height\": \"fill ? \\\"100%\\\" : null\", \"style.inset\": \"fill ? \\\"0\\\" : null\", \"style.background-size\": \"placeholder ? \\\"cover\\\" : null\", \"style.background-position\": \"placeholder ? \\\"50% 50%\\\" : null\", \"style.background-repeat\": \"placeholder ? \\\"no-repeat\\\" : null\", \"style.background-image\": \"placeholder ? generatePlaceholder(placeholder) : null\", \"style.filter\": \"placeholder && shouldBlurPlaceholder(placeholderConfig) ? \\\"blur(15px)\\\" : null\" } }, usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.14\", ngImport: i0, type: NgOptimizedImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'img[ngSrc]',\n                    host: {\n                        '[style.position]': 'fill ? \"absolute\" : null',\n                        '[style.width]': 'fill ? \"100%\" : null',\n                        '[style.height]': 'fill ? \"100%\" : null',\n                        '[style.inset]': 'fill ? \"0\" : null',\n                        '[style.background-size]': 'placeholder ? \"cover\" : null',\n                        '[style.background-position]': 'placeholder ? \"50% 50%\" : null',\n                        '[style.background-repeat]': 'placeholder ? \"no-repeat\" : null',\n                        '[style.background-image]': 'placeholder ? generatePlaceholder(placeholder) : null',\n                        '[style.filter]': `placeholder && shouldBlurPlaceholder(placeholderConfig) ? \"blur(${PLACEHOLDER_BLUR_AMOUNT}px)\" : null`,\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { ngSrc: [{\n                type: Input,\n                args: [{ required: true, transform: unwrapSafeUrl }]\n            }], ngSrcset: [{\n                type: Input\n            }], sizes: [{\n                type: Input\n            }], width: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], height: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], loading: [{\n                type: Input\n            }], priority: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], loaderParams: [{\n                type: Input\n            }], disableOptimizedSrcset: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], fill: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input,\n                args: [{ transform: booleanOrUrlAttribute }]\n            }], placeholderConfig: [{\n                type: Input\n            }], src: [{\n                type: Input\n            }], srcset: [{\n                type: Input\n            }] } });\n/***** Helpers *****/\n/**\n * Sorts provided config breakpoints and uses defaults.\n */\nfunction processConfig(config) {\n    let sortedBreakpoints = {};\n    if (config.breakpoints) {\n        sortedBreakpoints.breakpoints = config.breakpoints.sort((a, b) => a - b);\n    }\n    return Object.assign({}, _IMAGE_CONFIG_DEFAULTS, config, sortedBreakpoints);\n}\n/***** Assert functions *****/\n/**\n * Verifies that there is no `src` set on a host element.\n */\nfunction assertNoConflictingSrc(dir) {\n    if (dir.src) {\n        throw new _RuntimeError(2950 /* RuntimeErrorCode.UNEXPECTED_SRC_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`src\\` and \\`ngSrc\\` have been set. ` +\n            `Supplying both of these attributes breaks lazy loading. ` +\n            `The NgOptimizedImage directive sets \\`src\\` itself based on the value of \\`ngSrc\\`. ` +\n            `To fix this, please remove the \\`src\\` attribute.`);\n    }\n}\n/**\n * Verifies that there is no `srcset` set on a host element.\n */\nfunction assertNoConflictingSrcset(dir) {\n    if (dir.srcset) {\n        throw new _RuntimeError(2951 /* RuntimeErrorCode.UNEXPECTED_SRCSET_ATTR */, `${imgDirectiveDetails(dir.ngSrc)} both \\`srcset\\` and \\`ngSrcset\\` have been set. ` +\n            `Supplying both of these attributes breaks lazy loading. ` +\n            `The NgOptimizedImage directive sets \\`srcset\\` itself based on the value of ` +\n            `\\`ngSrcset\\`. To fix this, please remove the \\`srcset\\` attribute.`);\n    }\n}\n/**\n * Verifies that the `ngSrc` is not a Base64-encoded image.\n */\nfunction assertNotBase64Image(dir) {\n    let ngSrc = dir.ngSrc.trim();\n    if (ngSrc.startsWith('data:')) {\n        if (ngSrc.length > BASE64_IMG_MAX_LENGTH_IN_ERROR) {\n            ngSrc = ngSrc.substring(0, BASE64_IMG_MAX_LENGTH_IN_ERROR) + '...';\n        }\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`ngSrc\\` is a Base64-encoded string ` +\n            `(${ngSrc}). NgOptimizedImage does not support Base64-encoded strings. ` +\n            `To fix this, disable the NgOptimizedImage directive for this element ` +\n            `by removing \\`ngSrc\\` and using a standard \\`src\\` attribute instead.`);\n    }\n}\n/**\n * Verifies that the 'sizes' only includes responsive values.\n */\nfunction assertNoComplexSizes(dir) {\n    let sizes = dir.sizes;\n    if (sizes?.match(/((\\)|,)\\s|^)\\d+px/)) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`sizes\\` was set to a string including ` +\n            `pixel values. For automatic \\`srcset\\` generation, \\`sizes\\` must only include responsive ` +\n            `values, such as \\`sizes=\"50vw\"\\` or \\`sizes=\"(min-width: 768px) 50vw, 100vw\"\\`. ` +\n            `To fix this, modify the \\`sizes\\` attribute, or provide your own \\`ngSrcset\\` value directly.`);\n    }\n}\nfunction assertValidPlaceholder(dir, imageLoader) {\n    assertNoPlaceholderConfigWithoutPlaceholder(dir);\n    assertNoRelativePlaceholderWithoutLoader(dir, imageLoader);\n    assertNoOversizedDataUrl(dir);\n}\n/**\n * Verifies that placeholderConfig isn't being used without placeholder\n */\nfunction assertNoPlaceholderConfigWithoutPlaceholder(dir) {\n    if (dir.placeholderConfig && !dir.placeholder) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc, false)} \\`placeholderConfig\\` options were provided for an ` +\n            `image that does not use the \\`placeholder\\` attribute, and will have no effect.`);\n    }\n}\n/**\n * Warns if a relative URL placeholder is specified, but no loader is present to provide the small\n * image.\n */\nfunction assertNoRelativePlaceholderWithoutLoader(dir, imageLoader) {\n    if (dir.placeholder === true && imageLoader === noopImageLoader) {\n        throw new _RuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to true but ` +\n            `no image loader is configured (i.e. the default one is being used), ` +\n            `which would result in the same image being used for the primary image and its placeholder. ` +\n            `To fix this, provide a loader or remove the \\`placeholder\\` attribute from the image.`);\n    }\n}\n/**\n * Warns or throws an error if an oversized dataURL placeholder is provided.\n */\nfunction assertNoOversizedDataUrl(dir) {\n    if (dir.placeholder &&\n        typeof dir.placeholder === 'string' &&\n        dir.placeholder.startsWith('data:')) {\n        if (dir.placeholder.length > DATA_URL_ERROR_LIMIT) {\n            throw new _RuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n                `than ${DATA_URL_ERROR_LIMIT} characters. This is strongly discouraged, as large inline placeholders ` +\n                `directly increase the bundle size of Angular and hurt page load performance. To fix this, generate ` +\n                `a smaller data URL placeholder.`);\n        }\n        if (dir.placeholder.length > DATA_URL_WARN_LIMIT) {\n            console.warn(_formatRuntimeError(2965 /* RuntimeErrorCode.OVERSIZED_PLACEHOLDER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`placeholder\\` attribute is set to a data URL which is longer ` +\n                `than ${DATA_URL_WARN_LIMIT} characters. This is discouraged, as large inline placeholders ` +\n                `directly increase the bundle size of Angular and hurt page load performance. For better loading performance, ` +\n                `generate a smaller data URL placeholder.`));\n        }\n    }\n}\n/**\n * Verifies that the `ngSrc` is not a Blob URL.\n */\nfunction assertNotBlobUrl(dir) {\n    const ngSrc = dir.ngSrc.trim();\n    if (ngSrc.startsWith('blob:')) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrc\\` was set to a blob URL (${ngSrc}). ` +\n            `Blob URLs are not supported by the NgOptimizedImage directive. ` +\n            `To fix this, disable the NgOptimizedImage directive for this element ` +\n            `by removing \\`ngSrc\\` and using a regular \\`src\\` attribute instead.`);\n    }\n}\n/**\n * Verifies that the input is set to a non-empty string.\n */\nfunction assertNonEmptyInput(dir, name, value) {\n    const isString = typeof value === 'string';\n    const isEmptyString = isString && value.trim() === '';\n    if (!isString || isEmptyString) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${name}\\` has an invalid value ` +\n            `(\\`${value}\\`). To fix this, change the value to a non-empty string.`);\n    }\n}\n/**\n * Verifies that the `ngSrcset` is in a valid format, e.g. \"100w, 200w\" or \"1x, 2x\".\n */\nfunction assertValidNgSrcset(dir, value) {\n    if (value == null)\n        return;\n    assertNonEmptyInput(dir, 'ngSrcset', value);\n    const stringVal = value;\n    const isValidWidthDescriptor = VALID_WIDTH_DESCRIPTOR_SRCSET.test(stringVal);\n    const isValidDensityDescriptor = VALID_DENSITY_DESCRIPTOR_SRCSET.test(stringVal);\n    if (isValidDensityDescriptor) {\n        assertUnderDensityCap(dir, stringVal);\n    }\n    const isValidSrcset = isValidWidthDescriptor || isValidDensityDescriptor;\n    if (!isValidSrcset) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`ngSrcset\\` has an invalid value (\\`${value}\\`). ` +\n            `To fix this, supply \\`ngSrcset\\` using a comma-separated list of one or more width ` +\n            `descriptors (e.g. \"100w, 200w\") or density descriptors (e.g. \"1x, 2x\").`);\n    }\n}\nfunction assertUnderDensityCap(dir, value) {\n    const underDensityCap = value\n        .split(',')\n        .every((num) => num === '' || parseFloat(num) <= ABSOLUTE_SRCSET_DENSITY_CAP);\n    if (!underDensityCap) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` contains an unsupported image density:` +\n            `\\`${value}\\`. NgOptimizedImage generally recommends a max image density of ` +\n            `${RECOMMENDED_SRCSET_DENSITY_CAP}x but supports image densities up to ` +\n            `${ABSOLUTE_SRCSET_DENSITY_CAP}x. The human eye cannot distinguish between image densities ` +\n            `greater than ${RECOMMENDED_SRCSET_DENSITY_CAP}x - which makes them unnecessary for ` +\n            `most use cases. Images that will be pinch-zoomed are typically the primary use case for ` +\n            `${ABSOLUTE_SRCSET_DENSITY_CAP}x images. Please remove the high density descriptor and try again.`);\n    }\n}\n/**\n * Creates a `RuntimeError` instance to represent a situation when an input is set after\n * the directive has initialized.\n */\nfunction postInitInputChangeError(dir, inputName) {\n    let reason;\n    if (inputName === 'width' || inputName === 'height') {\n        reason =\n            `Changing \\`${inputName}\\` may result in different attribute value ` +\n                `applied to the underlying image element and cause layout shifts on a page.`;\n    }\n    else {\n        reason =\n            `Changing the \\`${inputName}\\` would have no effect on the underlying ` +\n                `image element, because the resource loading has already occurred.`;\n    }\n    return new _RuntimeError(2953 /* RuntimeErrorCode.UNEXPECTED_INPUT_CHANGE */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` was updated after initialization. ` +\n        `The NgOptimizedImage directive will not react to this input change. ${reason} ` +\n        `To fix this, either switch \\`${inputName}\\` to a static value ` +\n        `or wrap the image element in an @if that is gated on the necessary value.`);\n}\n/**\n * Verify that none of the listed inputs has changed.\n */\nfunction assertNoPostInitInputChange(dir, changes, inputs) {\n    inputs.forEach((input) => {\n        const isUpdated = changes.hasOwnProperty(input);\n        if (isUpdated && !changes[input].isFirstChange()) {\n            if (input === 'ngSrc') {\n                // When the `ngSrc` input changes, we detect that only in the\n                // `ngOnChanges` hook, thus the `ngSrc` is already set. We use\n                // `ngSrc` in the error message, so we use a previous value, but\n                // not the updated one in it.\n                dir = { ngSrc: changes[input].previousValue };\n            }\n            throw postInitInputChangeError(dir, input);\n        }\n    });\n}\n/**\n * Verifies that a specified input is a number greater than 0.\n */\nfunction assertGreaterThanZero(dir, inputValue, inputName) {\n    const validNumber = typeof inputValue === 'number' && inputValue > 0;\n    const validString = typeof inputValue === 'string' && /^\\d+$/.test(inputValue.trim()) && parseInt(inputValue) > 0;\n    if (!validNumber && !validString) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} \\`${inputName}\\` has an invalid value. ` +\n            `To fix this, provide \\`${inputName}\\` as a number greater than 0.`);\n    }\n}\n/**\n * Verifies that the rendered image is not visually distorted. Effectively this is checking:\n * - Whether the \"width\" and \"height\" attributes reflect the actual dimensions of the image.\n * - Whether image styling is \"correct\" (see below for a longer explanation).\n */\nfunction assertNoImageDistortion(dir, img, renderer) {\n    const callback = () => {\n        removeLoadListenerFn();\n        removeErrorListenerFn();\n        const computedStyle = window.getComputedStyle(img);\n        let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n        let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n        const boxSizing = computedStyle.getPropertyValue('box-sizing');\n        if (boxSizing === 'border-box') {\n            const paddingTop = computedStyle.getPropertyValue('padding-top');\n            const paddingRight = computedStyle.getPropertyValue('padding-right');\n            const paddingBottom = computedStyle.getPropertyValue('padding-bottom');\n            const paddingLeft = computedStyle.getPropertyValue('padding-left');\n            renderedWidth -= parseFloat(paddingRight) + parseFloat(paddingLeft);\n            renderedHeight -= parseFloat(paddingTop) + parseFloat(paddingBottom);\n        }\n        const renderedAspectRatio = renderedWidth / renderedHeight;\n        const nonZeroRenderedDimensions = renderedWidth !== 0 && renderedHeight !== 0;\n        const intrinsicWidth = img.naturalWidth;\n        const intrinsicHeight = img.naturalHeight;\n        const intrinsicAspectRatio = intrinsicWidth / intrinsicHeight;\n        const suppliedWidth = dir.width;\n        const suppliedHeight = dir.height;\n        const suppliedAspectRatio = suppliedWidth / suppliedHeight;\n        // Tolerance is used to account for the impact of subpixel rendering.\n        // Due to subpixel rendering, the rendered, intrinsic, and supplied\n        // aspect ratios of a correctly configured image may not exactly match.\n        // For example, a `width=4030 height=3020` image might have a rendered\n        // size of \"1062w, 796.48h\". (An aspect ratio of 1.334... vs. 1.333...)\n        const inaccurateDimensions = Math.abs(suppliedAspectRatio - intrinsicAspectRatio) > ASPECT_RATIO_TOLERANCE;\n        const stylingDistortion = nonZeroRenderedDimensions &&\n            Math.abs(intrinsicAspectRatio - renderedAspectRatio) > ASPECT_RATIO_TOLERANCE;\n        if (inaccurateDimensions) {\n            console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the image does not match ` +\n                `the aspect ratio indicated by the width and height attributes. ` +\n                `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n                `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nSupplied width and height attributes: ` +\n                `${suppliedWidth}w x ${suppliedHeight}h (aspect-ratio: ${round(suppliedAspectRatio)}). ` +\n                `\\nTo fix this, update the width and height attributes.`));\n        }\n        else if (stylingDistortion) {\n            console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the aspect ratio of the rendered image ` +\n                `does not match the image's intrinsic aspect ratio. ` +\n                `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h ` +\n                `(aspect-ratio: ${round(intrinsicAspectRatio)}). \\nRendered image size: ` +\n                `${renderedWidth}w x ${renderedHeight}h (aspect-ratio: ` +\n                `${round(renderedAspectRatio)}). \\nThis issue can occur if \"width\" and \"height\" ` +\n                `attributes are added to an image without updating the corresponding ` +\n                `image styling. To fix this, adjust image styling. In most cases, ` +\n                `adding \"height: auto\" or \"width: auto\" to the image styling will fix ` +\n                `this issue.`));\n        }\n        else if (!dir.ngSrcset && nonZeroRenderedDimensions) {\n            // If `ngSrcset` hasn't been set, sanity check the intrinsic size.\n            const recommendedWidth = RECOMMENDED_SRCSET_DENSITY_CAP * renderedWidth;\n            const recommendedHeight = RECOMMENDED_SRCSET_DENSITY_CAP * renderedHeight;\n            const oversizedWidth = intrinsicWidth - recommendedWidth >= OVERSIZED_IMAGE_TOLERANCE;\n            const oversizedHeight = intrinsicHeight - recommendedHeight >= OVERSIZED_IMAGE_TOLERANCE;\n            if (oversizedWidth || oversizedHeight) {\n                console.warn(_formatRuntimeError(2960 /* RuntimeErrorCode.OVERSIZED_IMAGE */, `${imgDirectiveDetails(dir.ngSrc)} the intrinsic image is significantly ` +\n                    `larger than necessary. ` +\n                    `\\nRendered image size: ${renderedWidth}w x ${renderedHeight}h. ` +\n                    `\\nIntrinsic image size: ${intrinsicWidth}w x ${intrinsicHeight}h. ` +\n                    `\\nRecommended intrinsic image size: ${recommendedWidth}w x ${recommendedHeight}h. ` +\n                    `\\nNote: Recommended intrinsic image size is calculated assuming a maximum DPR of ` +\n                    `${RECOMMENDED_SRCSET_DENSITY_CAP}. To improve loading time, resize the image ` +\n                    `or consider using the \"ngSrcset\" and \"sizes\" attributes.`));\n            }\n        }\n    };\n    const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n    // We only listen to the `error` event to remove the `load` event listener because it will not be\n    // fired if the image fails to load. This is done to prevent memory leaks in development mode\n    // because image elements aren't garbage-collected properly. It happens because zone.js stores the\n    // event listener directly on the element and closures capture `dir`.\n    const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n        removeLoadListenerFn();\n        removeErrorListenerFn();\n    });\n    callOnLoadIfImageIsLoaded(img, callback);\n}\n/**\n * Verifies that a specified input is set.\n */\nfunction assertNonEmptyWidthAndHeight(dir) {\n    let missingAttributes = [];\n    if (dir.width === undefined)\n        missingAttributes.push('width');\n    if (dir.height === undefined)\n        missingAttributes.push('height');\n    if (missingAttributes.length > 0) {\n        throw new _RuntimeError(2954 /* RuntimeErrorCode.REQUIRED_INPUT_MISSING */, `${imgDirectiveDetails(dir.ngSrc)} these required attributes ` +\n            `are missing: ${missingAttributes.map((attr) => `\"${attr}\"`).join(', ')}. ` +\n            `Including \"width\" and \"height\" attributes will prevent image-related layout shifts. ` +\n            `To fix this, include \"width\" and \"height\" attributes on the image tag or turn on ` +\n            `\"fill\" mode with the \\`fill\\` attribute.`);\n    }\n}\n/**\n * Verifies that width and height are not set. Used in fill mode, where those attributes don't make\n * sense.\n */\nfunction assertEmptyWidthAndHeight(dir) {\n    if (dir.width || dir.height) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the attributes \\`height\\` and/or \\`width\\` are present ` +\n            `along with the \\`fill\\` attribute. Because \\`fill\\` mode causes an image to fill its containing ` +\n            `element, the size attributes have no effect and should be removed.`);\n    }\n}\n/**\n * Verifies that the rendered image has a nonzero height. If the image is in fill mode, provides\n * guidance that this can be caused by the containing element's CSS position property.\n */\nfunction assertNonZeroRenderedHeight(dir, img, renderer) {\n    const callback = () => {\n        removeLoadListenerFn();\n        removeErrorListenerFn();\n        const renderedHeight = img.clientHeight;\n        if (dir.fill && renderedHeight === 0) {\n            console.warn(_formatRuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the height of the fill-mode image is zero. ` +\n                `This is likely because the containing element does not have the CSS 'position' ` +\n                `property set to one of the following: \"relative\", \"fixed\", or \"absolute\". ` +\n                `To fix this problem, make sure the container element has the CSS 'position' ` +\n                `property defined and the height of the element is not zero.`));\n        }\n    };\n    const removeLoadListenerFn = renderer.listen(img, 'load', callback);\n    // See comments in the `assertNoImageDistortion`.\n    const removeErrorListenerFn = renderer.listen(img, 'error', () => {\n        removeLoadListenerFn();\n        removeErrorListenerFn();\n    });\n    callOnLoadIfImageIsLoaded(img, callback);\n}\n/**\n * Verifies that the `loading` attribute is set to a valid input &\n * is not used on priority images.\n */\nfunction assertValidLoadingInput(dir) {\n    if (dir.loading && dir.priority) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n            `was used on an image that was marked \"priority\". ` +\n            `Setting \\`loading\\` on priority images is not allowed ` +\n            `because these images will always be eagerly loaded. ` +\n            `To fix this, remove the “loading” attribute from the priority image.`);\n    }\n    const validInputs = ['auto', 'eager', 'lazy'];\n    if (typeof dir.loading === 'string' && !validInputs.includes(dir.loading)) {\n        throw new _RuntimeError(2952 /* RuntimeErrorCode.INVALID_INPUT */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loading\\` attribute ` +\n            `has an invalid value (\\`${dir.loading}\\`). ` +\n            `To fix this, provide a valid value (\"lazy\", \"eager\", or \"auto\").`);\n    }\n}\n/**\n * Warns if NOT using a loader (falling back to the generic loader) and\n * the image appears to be hosted on one of the image CDNs for which\n * we do have a built-in image loader. Suggests switching to the\n * built-in loader.\n *\n * @param ngSrc Value of the ngSrc attribute\n * @param imageLoader ImageLoader provided\n */\nfunction assertNotMissingBuiltInLoader(ngSrc, imageLoader) {\n    if (imageLoader === noopImageLoader) {\n        let builtInLoaderName = '';\n        for (const loader of BUILT_IN_LOADERS) {\n            if (loader.testUrl(ngSrc)) {\n                builtInLoaderName = loader.name;\n                break;\n            }\n        }\n        if (builtInLoaderName) {\n            console.warn(_formatRuntimeError(2962 /* RuntimeErrorCode.MISSING_BUILTIN_LOADER */, `NgOptimizedImage: It looks like your images may be hosted on the ` +\n                `${builtInLoaderName} CDN, but your app is not using Angular's ` +\n                `built-in loader for that CDN. We recommend switching to use ` +\n                `the built-in by calling \\`provide${builtInLoaderName}Loader()\\` ` +\n                `in your \\`providers\\` and passing it your instance's base URL. ` +\n                `If you don't want to use the built-in loader, define a custom ` +\n                `loader function using IMAGE_LOADER to silence this warning.`));\n        }\n    }\n}\n/**\n * Warns if ngSrcset is present and no loader is configured (i.e. the default one is being used).\n */\nfunction assertNoNgSrcsetWithoutLoader(dir, imageLoader) {\n    if (dir.ngSrcset && imageLoader === noopImageLoader) {\n        console.warn(_formatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`ngSrcset\\` attribute is present but ` +\n            `no image loader is configured (i.e. the default one is being used), ` +\n            `which would result in the same image being used for all configured sizes. ` +\n            `To fix this, provide a loader or remove the \\`ngSrcset\\` attribute from the image.`));\n    }\n}\n/**\n * Warns if loaderParams is present and no loader is configured (i.e. the default one is being\n * used).\n */\nfunction assertNoLoaderParamsWithoutLoader(dir, imageLoader) {\n    if (dir.loaderParams && imageLoader === noopImageLoader) {\n        console.warn(_formatRuntimeError(2963 /* RuntimeErrorCode.MISSING_NECESSARY_LOADER */, `${imgDirectiveDetails(dir.ngSrc)} the \\`loaderParams\\` attribute is present but ` +\n            `no image loader is configured (i.e. the default one is being used), ` +\n            `which means that the loaderParams data will not be consumed and will not affect the URL. ` +\n            `To fix this, provide a custom loader or remove the \\`loaderParams\\` attribute from the image.`));\n    }\n}\n/**\n * Warns if the priority attribute is used too often on page load\n */\nasync function assetPriorityCountBelowThreshold(appRef) {\n    if (IMGS_WITH_PRIORITY_ATTR_COUNT === 0) {\n        IMGS_WITH_PRIORITY_ATTR_COUNT++;\n        await appRef.whenStable();\n        if (IMGS_WITH_PRIORITY_ATTR_COUNT > PRIORITY_COUNT_THRESHOLD) {\n            console.warn(_formatRuntimeError(2966 /* RuntimeErrorCode.TOO_MANY_PRIORITY_ATTRIBUTES */, `NgOptimizedImage: The \"priority\" attribute is set to true more than ${PRIORITY_COUNT_THRESHOLD} times (${IMGS_WITH_PRIORITY_ATTR_COUNT} times). ` +\n                `Marking too many images as \"high\" priority can hurt your application's LCP (https://web.dev/lcp). ` +\n                `\"Priority\" should only be set on the image expected to be the page's LCP element.`));\n        }\n    }\n    else {\n        IMGS_WITH_PRIORITY_ATTR_COUNT++;\n    }\n}\n/**\n * Warns if placeholder's dimension are over a threshold.\n *\n * This assert function is meant to only run on the browser.\n */\nfunction assertPlaceholderDimensions(dir, imgElement) {\n    const computedStyle = window.getComputedStyle(imgElement);\n    let renderedWidth = parseFloat(computedStyle.getPropertyValue('width'));\n    let renderedHeight = parseFloat(computedStyle.getPropertyValue('height'));\n    if (renderedWidth > PLACEHOLDER_DIMENSION_LIMIT || renderedHeight > PLACEHOLDER_DIMENSION_LIMIT) {\n        console.warn(_formatRuntimeError(2967 /* RuntimeErrorCode.PLACEHOLDER_DIMENSION_LIMIT_EXCEEDED */, `${imgDirectiveDetails(dir.ngSrc)} it uses a placeholder image, but at least one ` +\n            `of the dimensions attribute (height or width) exceeds the limit of ${PLACEHOLDER_DIMENSION_LIMIT}px. ` +\n            `To fix this, use a smaller image as a placeholder.`));\n    }\n}\nfunction callOnLoadIfImageIsLoaded(img, callback) {\n    // https://html.spec.whatwg.org/multipage/embedded-content.html#dom-img-complete\n    // The spec defines that `complete` is truthy once its request state is fully available.\n    // The image may already be available if it’s loaded from the browser cache.\n    // In that case, the `load` event will not fire at all, meaning that all setup\n    // callbacks listening for the `load` event will not be invoked.\n    // In Safari, there is a known behavior where the `complete` property of an\n    // `HTMLImageElement` may sometimes return `true` even when the image is not fully loaded.\n    // Checking both `img.complete` and `img.naturalWidth` is the most reliable way to\n    // determine if an image has been fully loaded, especially in browsers where the\n    // `complete` property may return `true` prematurely.\n    if (img.complete && img.naturalWidth) {\n        callback();\n    }\n}\nfunction round(input) {\n    return Number.isInteger(input) ? input : input.toFixed(2);\n}\n// Transform function to handle SafeValue input for ngSrc. This doesn't do any sanitization,\n// as that is not needed for img.src and img.srcset. This transform is purely for compatibility.\nfunction unwrapSafeUrl(value) {\n    if (typeof value === 'string') {\n        return value;\n    }\n    return _unwrapSafeValue(value);\n}\n// Transform function to handle inputs which may be booleans, strings, or string representations\n// of boolean values. Used for the placeholder attribute.\nfunction booleanOrUrlAttribute(value) {\n    if (typeof value === 'string' && value !== 'true' && value !== 'false' && value !== '') {\n        return value;\n    }\n    return booleanAttribute(value);\n}\n\nexport { DOCUMENT, IMAGE_LOADER, NgOptimizedImage, PRECONNECT_CHECK_BLOCKLIST, VERSION, ViewportScroller, isPlatformBrowser, provideCloudflareLoader, provideCloudinaryLoader, provideImageKitLoader, provideImgixLoader, provideNetlifyLoader, registerLocaleData, NullViewportScroller as ɵNullViewportScroller };\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,EAAEC,YAAY,EAAEC,YAAY,EAAEC,yBAAyB,EAAEC,0BAA0B,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,SAAS,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,aAAa,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,OAAO,IAAIC,KAAK,EAAED,OAAO,EAAEE,cAAc,EAAEC,IAAI,EAAEC,WAAW,EAAEC,oBAAoB,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,OAAO,EAAEC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,uBAAuB,EAAEC,mBAAmB,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,4BAA4B,EAAEC,wBAAwB,EAAEC,uBAAuB,EAAEC,WAAW,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAEC,qBAAqB,EAAEC,yBAAyB,QAAQ,8BAA8B;AAC1nC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,mBAAmB,IAAIC,mBAAmB,EAAEC,OAAO,EAAEC,kBAAkB,IAAIC,kBAAkB,EAAEC,MAAM,EAAEC,cAAc,EAAEC,aAAa,IAAIC,aAAa,EAAEC,mBAAmB,IAAIC,mBAAmB,EAAEC,WAAW,EAAEC,UAAU,EAAEC,aAAa,IAAIC,aAAa,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,uBAAuB,IAAIC,uBAAuB,EAAEC,MAAM,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,sBAAsB,IAAIC,sBAAsB,EAAEC,gBAAgB,IAAIC,gBAAgB,EAAEC,KAAK,EAAEC,SAAS,QAAQ,eAAe;AACnjB,SAASlB,aAAa,IAAImB,YAAY,QAAQ,eAAe;AAC7D,SAASC,iBAAiB,QAAQ,oBAAoB;AACtD,SAASC,UAAU,EAAEC,gBAAgB,EAAEC,mBAAmB,IAAIC,oBAAoB,EAAEC,kBAAkB,IAAIC,mBAAmB,EAAEC,gBAAgB,IAAIC,iBAAiB,QAAQ,oBAAoB;AAChM,SAASC,QAAQ,QAAQ,2BAA2B;AACpD,SAASC,aAAa,EAAEC,uBAAuB,EAAEC,oBAAoB,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,gBAAgB,EAAEC,UAAU,IAAIC,WAAW,EAAEC,MAAM,IAAIC,OAAO,EAAEC,oBAAoB,IAAIC,qBAAqB,EAAEC,iBAAiB,IAAIC,kBAAkB,QAAQ,yBAAyB;AAChT,SAASC,kBAAkB,IAAIC,mBAAmB,QAAQ,oCAAoC;AAC9F,OAAO,MAAM;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,SAAS,EAAE;EACnD,OAAO9D,mBAAmB,CAAC4D,IAAI,EAAEC,QAAQ,EAAEC,SAAS,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,IAAI9D,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA,MAAM+D,gBAAgB,CAAC;EACnB;EACA;EACA;EACA,OAAOC,KAAK,GAAG,0BAA0B,eAAgB9D,kBAAkB,CAAC;IACxE+D,KAAK,EAAEF,gBAAgB;IACvBG,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAEA,CAAA,KAAM,OAAOC,YAAY,KAAK,WAAW,IAAIA,YAAY,GAC5D,IAAIC,oBAAoB,CAAC,CAAC,GAC1B,IAAIC,uBAAuB,CAACnE,MAAM,CAACqC,QAAQ,CAAC,EAAE+B,MAAM;EAC9D,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,MAAMD,uBAAuB,CAAC;EAC1BE,QAAQ;EACRD,MAAM;EACNE,MAAM,GAAGA,CAAA,KAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACrBC,WAAWA,CAACF,QAAQ,EAAED,MAAM,EAAE;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACD,MAAM,GAAGA,MAAM;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACII,SAASA,CAACF,MAAM,EAAE;IACd,IAAIG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;MACvB,IAAI,CAACA,MAAM,GAAG,MAAMA,MAAM;IAC9B,CAAC,MACI;MACD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACxB;EACJ;EACA;AACJ;AACA;AACA;EACIK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,IAAI,CAACP,MAAM,CAACQ,OAAO,EAAE,IAAI,CAACR,MAAM,CAACS,OAAO,CAAC;EACrD;EACA;AACJ;AACA;AACA;EACIC,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACX,MAAM,CAACY,QAAQ,CAACD,QAAQ,CAAC,CAAC,CAAC,EAAEA,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,cAAcA,CAACC,MAAM,EAAE;IACnB,MAAMC,UAAU,GAAGC,sBAAsB,CAAC,IAAI,CAACf,QAAQ,EAAEa,MAAM,CAAC;IAChE,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACE,eAAe,CAACF,UAAU,CAAC;MAChC;MACA;MACA;MACA;MACA;MACA;MACAA,UAAU,CAACG,KAAK,CAAC,CAAC;IACtB;EACJ;EACA;AACJ;AACA;EACIC,2BAA2BA,CAACC,iBAAiB,EAAE;IAC3C,IAAI,CAACpB,MAAM,CAACqB,OAAO,CAACD,iBAAiB,GAAGA,iBAAiB;EAC7D;EACA;AACJ;AACA;AACA;AACA;AACA;EACIH,eAAeA,CAACK,EAAE,EAAE;IAChB,MAAMC,IAAI,GAAGD,EAAE,CAACE,qBAAqB,CAAC,CAAC;IACvC,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,GAAG,IAAI,CAACzB,MAAM,CAAC0B,WAAW;IAChD,MAAMC,GAAG,GAAGJ,IAAI,CAACI,GAAG,GAAG,IAAI,CAAC3B,MAAM,CAAC4B,WAAW;IAC9C,MAAM1B,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC;IAC5B,IAAI,CAACF,MAAM,CAACY,QAAQ,CAACa,IAAI,GAAGvB,MAAM,CAAC,CAAC,CAAC,EAAEyB,GAAG,GAAGzB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3D;AACJ;AACA,SAASc,sBAAsBA,CAACf,QAAQ,EAAEa,MAAM,EAAE;EAC9C,MAAMe,cAAc,GAAG5B,QAAQ,CAAC6B,cAAc,CAAChB,MAAM,CAAC,IAAIb,QAAQ,CAAC8B,iBAAiB,CAACjB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/F,IAAIe,cAAc,EAAE;IAChB,OAAOA,cAAc;EACzB;EACA;EACA;EACA,IAAI,OAAO5B,QAAQ,CAAC+B,gBAAgB,KAAK,UAAU,IAC/C/B,QAAQ,CAACgC,IAAI,IACb,OAAOhC,QAAQ,CAACgC,IAAI,CAACC,YAAY,KAAK,UAAU,EAAE;IAClD,MAAMC,UAAU,GAAGlC,QAAQ,CAAC+B,gBAAgB,CAAC/B,QAAQ,CAACgC,IAAI,EAAEG,UAAU,CAACC,YAAY,CAAC;IACpF,IAAIC,WAAW,GAAGH,UAAU,CAACG,WAAW;IACxC,OAAOA,WAAW,EAAE;MAChB,MAAMC,UAAU,GAAGD,WAAW,CAACC,UAAU;MACzC,IAAIA,UAAU,EAAE;QACZ;QACA;QACA,MAAMC,MAAM,GAAGD,UAAU,CAACT,cAAc,CAAChB,MAAM,CAAC,IAAIyB,UAAU,CAACE,aAAa,CAAC,UAAU3B,MAAM,IAAI,CAAC;QAClG,IAAI0B,MAAM,EAAE;UACR,OAAOA,MAAM;QACjB;MACJ;MACAF,WAAW,GAAGH,UAAU,CAACO,QAAQ,CAAC,CAAC;IACvC;EACJ;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA,MAAM5C,oBAAoB,CAAC;EACvB;AACJ;AACA;EACIM,SAASA,CAACF,MAAM,EAAE,CAAE;EACpB;AACJ;AACA;EACIK,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;EACjB;EACA;AACJ;AACA;EACIG,gBAAgBA,CAACC,QAAQ,EAAE,CAAE;EAC7B;AACJ;AACA;EACIE,cAAcA,CAAC8B,MAAM,EAAE,CAAE;EACzB;AACJ;AACA;EACIxB,2BAA2BA,CAACC,iBAAiB,EAAE,CAAE;AACrD;;AAEA;AACA;AACA;AACA,MAAMwB,mBAAmB,GAAG,IAAI;;AAEhC;AACA,SAASC,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACtB;EACA,OAAOC,aAAa,CAACF,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACH,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACH,GAAG,EAAEC,GAAG,CAACG,QAAQ,CAACC,IAAI,CAAC;AAC9E;AACA;AACA,SAASH,aAAaA,CAACF,GAAG,EAAE;EACxB,OAAO,cAAc,CAACM,IAAI,CAACN,GAAG,CAAC;AACnC;AACA;AACA;AACA,SAASO,eAAeA,CAACC,GAAG,EAAE;EAC1B,OAAON,aAAa,CAACM,GAAG,CAAC,GAAG,IAAIL,GAAG,CAACK,GAAG,CAAC,CAACC,QAAQ,GAAGD,GAAG;AAC3D;AACA,SAASE,WAAWA,CAACC,IAAI,EAAE;EACvB,MAAMC,QAAQ,GAAG,OAAOD,IAAI,KAAK,QAAQ;EACzC,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACjC,OAAO,KAAK;EAChB;EACA;EACA,IAAI;IACA,MAAML,GAAG,GAAG,IAAIL,GAAG,CAACQ,IAAI,CAAC;IACzB,OAAO,IAAI;EACf,CAAC,CACD,MAAM;IACF,OAAO,KAAK;EAChB;AACJ;AACA,SAASG,aAAaA,CAACH,IAAI,EAAE;EACzB,OAAOA,IAAI,CAACI,QAAQ,CAAC,GAAG,CAAC,GAAGJ,IAAI,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAGL,IAAI;AACxD;AACA,SAASM,YAAYA,CAACjB,GAAG,EAAE;EACvB,OAAOA,GAAG,CAACkB,UAAU,CAAC,GAAG,CAAC,GAAGlB,GAAG,CAACgB,KAAK,CAAC,CAAC,CAAC,GAAGhB,GAAG;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmB,eAAe,GAAIC,MAAM,IAAKA,MAAM,CAACpB,GAAG;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,YAAY,GAAG,IAAItI,cAAc,CAACuI,SAAS,GAAG,aAAa,GAAG,EAAE,EAAE;EACpEzE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAMqE;AACnB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,iBAAiBA,CAACC,UAAU,EAAEC,WAAW,EAAE;EAChD,OAAO,SAASC,kBAAkBA,CAACf,IAAI,EAAE;IACrC,IAAI,CAACD,WAAW,CAACC,IAAI,CAAC,EAAE;MACpBgB,qBAAqB,CAAChB,IAAI,EAAEc,WAAW,IAAI,EAAE,CAAC;IAClD;IACA;IACA;IACAd,IAAI,GAAGG,aAAa,CAACH,IAAI,CAAC;IAC1B,MAAMiB,QAAQ,GAAIR,MAAM,IAAK;MACzB,IAAIlB,aAAa,CAACkB,MAAM,CAACpB,GAAG,CAAC,EAAE;QAC3B;QACA;QACA;QACA;QACA;QACA6B,+BAA+B,CAAClB,IAAI,EAAES,MAAM,CAACpB,GAAG,CAAC;MACrD;MACA,OAAOwB,UAAU,CAACb,IAAI,EAAE;QAAE,GAAGS,MAAM;QAAEpB,GAAG,EAAEiB,YAAY,CAACG,MAAM,CAACpB,GAAG;MAAE,CAAC,CAAC;IACzE,CAAC;IACD,MAAM8B,SAAS,GAAG,CAAC;MAAEC,OAAO,EAAEV,YAAY;MAAEW,QAAQ,EAAEJ;IAAS,CAAC,CAAC;IACjE,OAAOE,SAAS;EACpB,CAAC;AACL;AACA,SAASH,qBAAqBA,CAAChB,IAAI,EAAEc,WAAW,EAAE;EAC9C,MAAM,IAAIxI,aAAa,CAAC,IAAI,CAAC,iDAAiDqI,SAAS,IACnF,gDAAgDX,IAAI,OAAO,GACvD,kEAAkEc,WAAW,CAACQ,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;AACzG;AACA,SAASJ,+BAA+BA,CAAClB,IAAI,EAAEH,GAAG,EAAE;EAChD,MAAM,IAAIvH,aAAa,CAAC,IAAI,CAAC,iDAAiDqI,SAAS,IACnF,kFAAkFd,GAAG,IAAI,GACrF,6DAA6D,GAC7D,iDAAiD,GACjD,oEAAoE,GACpE,iCAAiCG,IAAI,MAAM,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,uBAAuB,GAAGX,iBAAiB,CAACY,mBAAmB,EAAEb,SAAS,GAAG,CAAC,uDAAuD,CAAC,GAAGc,SAAS,CAAC;AACzJ,SAASD,mBAAmBA,CAACxB,IAAI,EAAES,MAAM,EAAE;EACvC,IAAIiB,MAAM,GAAG,aAAa;EAC1B,IAAIjB,MAAM,CAACkB,KAAK,EAAE;IACdD,MAAM,IAAI,UAAUjB,MAAM,CAACkB,KAAK,EAAE;EACtC;EACA;EACA,IAAIlB,MAAM,CAACmB,aAAa,EAAE;IACtBF,MAAM,IAAI,YAAYvC,mBAAmB,EAAE;EAC/C;EACA;EACA;EACA,OAAO,GAAGa,IAAI,kBAAkB0B,MAAM,IAAIjB,MAAM,CAACpB,GAAG,EAAE;AAC1D;;AAEA;AACA;AACA;AACA,MAAMwC,oBAAoB,GAAG;EACzBC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAEC;AACb,CAAC;AACD,MAAMC,uBAAuB,GAAG,yCAAyC;AACzE;AACA;AACA;AACA,SAASD,eAAeA,CAACnC,GAAG,EAAE;EAC1B,OAAOoC,uBAAuB,CAACtC,IAAI,CAACE,GAAG,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,uBAAuB,GAAGtB,iBAAiB,CAACuB,mBAAmB,EAAExB,SAAS,GAC1E,CACE,mCAAmC,EACnC,+BAA+B,EAC/B,8BAA8B,CACjC,GACCc,SAAS,CAAC;AAChB,SAASU,mBAAmBA,CAACnC,IAAI,EAAES,MAAM,EAAE;EACvC;EACA;EACA;EACA;EACA;EACA;EACA,MAAM2B,OAAO,GAAG3B,MAAM,CAACmB,aAAa,GAAG,YAAY,GAAG,QAAQ;EAC9D,IAAIF,MAAM,GAAG,UAAUU,OAAO,EAAE;EAChC,IAAI3B,MAAM,CAACkB,KAAK,EAAE;IACdD,MAAM,IAAI,MAAMjB,MAAM,CAACkB,KAAK,EAAE;EAClC;EACA,IAAIlB,MAAM,CAAC4B,YAAY,GAAG,SAAS,CAAC,EAAE;IAClCX,MAAM,IAAI,QAAQ;EACtB;EACA,OAAO,GAAG1B,IAAI,iBAAiB0B,MAAM,IAAIjB,MAAM,CAACpB,GAAG,EAAE;AACzD;;AAEA;AACA;AACA;AACA,MAAMiD,kBAAkB,GAAG;EACvBR,IAAI,EAAE,UAAU;EAChBC,OAAO,EAAEQ;AACb,CAAC;AACD,MAAMC,sBAAsB,GAAG,sCAAsC;AACrE;AACA;AACA;AACA,SAASD,aAAaA,CAAC1C,GAAG,EAAE;EACxB,OAAO2C,sBAAsB,CAAC7C,IAAI,CAACE,GAAG,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4C,qBAAqB,GAAG7B,iBAAiB,CAAC8B,iBAAiB,EAAE/B,SAAS,GAAG,CAAC,+BAA+B,EAAE,8BAA8B,CAAC,GAAGc,SAAS,CAAC;AAC7J,SAASiB,iBAAiBA,CAAC1C,IAAI,EAAES,MAAM,EAAE;EACrC;EACA;EACA,MAAM;IAAEpB,GAAG;IAAEsC;EAAM,CAAC,GAAGlB,MAAM;EAC7B,MAAMiB,MAAM,GAAG,EAAE;EACjB,IAAIC,KAAK,EAAE;IACPD,MAAM,CAACiB,IAAI,CAAC,KAAKhB,KAAK,EAAE,CAAC;EAC7B;EACA;EACA,IAAIlB,MAAM,CAACmB,aAAa,EAAE;IACtBF,MAAM,CAACiB,IAAI,CAAC,KAAKxD,mBAAmB,EAAE,CAAC;EAC3C;EACA,MAAMyD,WAAW,GAAGlB,MAAM,CAACmB,MAAM,GAAG,CAAC7C,IAAI,EAAE,MAAM0B,MAAM,CAACJ,IAAI,CAAC,GAAG,CAAC,EAAE,EAAEjC,GAAG,CAAC,GAAG,CAACW,IAAI,EAAEX,GAAG,CAAC;EACvF,MAAMQ,GAAG,GAAG,IAAIL,GAAG,CAACoD,WAAW,CAACtB,IAAI,CAAC,GAAG,CAAC,CAAC;EAC1C,OAAOzB,GAAG,CAACH,IAAI;AACnB;;AAEA;AACA;AACA;AACA,MAAMoD,eAAe,GAAG;EACpBhB,IAAI,EAAE,OAAO;EACbC,OAAO,EAAEgB;AACb,CAAC;AACD,MAAMC,kBAAkB,GAAG,oCAAoC;AAC/D;AACA;AACA;AACA,SAASD,UAAUA,CAAClD,GAAG,EAAE;EACrB,OAAOmD,kBAAkB,CAACrD,IAAI,CAACE,GAAG,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,kBAAkB,GAAGrC,iBAAiB,CAACsC,cAAc,EAAEvC,SAAS,GAAG,CAAC,6BAA6B,CAAC,GAAGc,SAAS,CAAC;AACrH,SAASyB,cAAcA,CAAClD,IAAI,EAAES,MAAM,EAAE;EAClC,MAAMZ,GAAG,GAAG,IAAIL,GAAG,CAAC,GAAGQ,IAAI,IAAIS,MAAM,CAACpB,GAAG,EAAE,CAAC;EAC5C;EACAQ,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;EACtC,IAAI3C,MAAM,CAACkB,KAAK,EAAE;IACd9B,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,GAAG,EAAE3C,MAAM,CAACkB,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;EACtD;EACA;EACA,IAAI5C,MAAM,CAACmB,aAAa,EAAE;IACtB/B,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,GAAG,EAAEjE,mBAAmB,CAAC;EAClD;EACA,OAAOU,GAAG,CAACH,IAAI;AACnB;;AAEA;AACA;AACA;AACA,MAAM4D,iBAAiB,GAAG;EACtBxB,IAAI,EAAE,SAAS;EACfC,OAAO,EAAEwB;AACb,CAAC;AACD,MAAMC,oBAAoB,GAAG,sCAAsC;AACnE;AACA;AACA;AACA;AACA;AACA,SAASD,YAAYA,CAAC1D,GAAG,EAAE;EACvB,OAAO2D,oBAAoB,CAAC7D,IAAI,CAACE,GAAG,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS4D,oBAAoBA,CAACzD,IAAI,EAAE;EAChC,IAAIA,IAAI,IAAI,CAACD,WAAW,CAACC,IAAI,CAAC,EAAE;IAC5B,MAAM,IAAI1H,aAAa,CAAC,IAAI,CAAC,iDAAiDqI,SAAS,IACnF,gDAAgDX,IAAI,OAAO,GACvD,yGAAyG,CAAC;EACtH;EACA,IAAIA,IAAI,EAAE;IACN,MAAMH,GAAG,GAAG,IAAIL,GAAG,CAACQ,IAAI,CAAC;IACzBA,IAAI,GAAGH,GAAG,CAAC6D,MAAM;EACrB;EACA,MAAMzC,QAAQ,GAAIR,MAAM,IAAK;IACzB,OAAOkD,gBAAgB,CAAClD,MAAM,EAAET,IAAI,CAAC;EACzC,CAAC;EACD,MAAMmB,SAAS,GAAG,CAAC;IAAEC,OAAO,EAAEV,YAAY;IAAEW,QAAQ,EAAEJ;EAAS,CAAC,CAAC;EACjE,OAAOE,SAAS;AACpB;AACA,MAAMyC,WAAW,GAAG,IAAIC,GAAG,CAAC,CACxB,CAAC,QAAQ,EAAE,GAAG,CAAC,EACf,CAAC,KAAK,EAAE,KAAK,CAAC,EACd,CAAC,SAAS,EAAE,GAAG,CAAC,EAChB,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,UAAU,EAAE,UAAU,CAAC,CAC3B,CAAC;AACF,SAASF,gBAAgBA,CAAClD,MAAM,EAAET,IAAI,EAAE;EACpC;EACA,MAAMH,GAAG,GAAG,IAAIL,GAAG,CAACQ,IAAI,IAAI,YAAY,CAAC;EACzCH,GAAG,CAACiE,QAAQ,GAAG,kBAAkB;EACjC,IAAI,CAACvE,aAAa,CAACkB,MAAM,CAACpB,GAAG,CAAC,IAAI,CAACoB,MAAM,CAACpB,GAAG,CAACkB,UAAU,CAAC,GAAG,CAAC,EAAE;IAC3DE,MAAM,CAACpB,GAAG,GAAG,GAAG,GAAGoB,MAAM,CAACpB,GAAG;EACjC;EACAQ,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,KAAK,EAAE3C,MAAM,CAACpB,GAAG,CAAC;EACvC,IAAIoB,MAAM,CAACkB,KAAK,EAAE;IACd9B,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,GAAG,EAAE3C,MAAM,CAACkB,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;EACtD;EACA;EACA;EACA,MAAMU,aAAa,GAAGtD,MAAM,CAAC4B,YAAY,GAAG,SAAS,CAAC,IAAI5B,MAAM,CAAC4B,YAAY,GAAG,GAAG,CAAC;EACpF,IAAI5B,MAAM,CAACmB,aAAa,IAAI,CAACmC,aAAa,EAAE;IACxClE,GAAG,CAACsD,YAAY,CAACC,GAAG,CAAC,GAAG,EAAEjE,mBAAmB,CAAC;EAClD;EACA,KAAK,MAAM,CAAC6E,KAAK,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAAC1D,MAAM,CAAC4B,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE;IACpE,IAAIuB,WAAW,CAACQ,GAAG,CAACJ,KAAK,CAAC,EAAE;MACxBnE,GAAG,CAACsD,YAAY,CAACC,GAAG,CAACQ,WAAW,CAACS,GAAG,CAACL,KAAK,CAAC,EAAEC,KAAK,CAACZ,QAAQ,CAAC,CAAC,CAAC;IAClE,CAAC,MACI;MACD,IAAI1C,SAAS,EAAE;QACX2D,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,iDAAiD,4FAA4FwL,KAAK,MAAM,CAAC,CAAC;MACpM;IACJ;EACJ;EACA;EACA,OAAOnE,GAAG,CAACC,QAAQ,KAAK,GAAG,GAAGD,GAAG,CAACH,IAAI,CAAC8E,OAAO,CAAC3E,GAAG,CAAC6D,MAAM,EAAE,EAAE,CAAC,GAAG7D,GAAG,CAACH,IAAI;AAC7E;;AAEA;AACA,SAAS+E,mBAAmBA,CAACC,KAAK,EAAEC,YAAY,GAAG,IAAI,EAAE;EACrD,MAAMC,SAAS,GAAGD,YAAY,GACxB,oDAAoDD,KAAK,OAAO,GAChE,EAAE;EACR,OAAO,kCAAkCE,SAAS,mBAAmB;AACzE;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,SAAS,EAAE;EAC9B,IAAI,CAACnE,SAAS,EAAE;IACZ,MAAM,IAAIrI,aAAa,CAAC,IAAI,CAAC,+DAA+D,gCAAgCwM,SAAS,qBAAqB,GACtJ,uEAAuE,CAAC;EAChF;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnB;EACAC,MAAM,GAAG,IAAInB,GAAG,CAAC,CAAC;EAClBtH,MAAM,GAAG,IAAI;EACb0I,QAAQ,GAAG,IAAI;EACfvI,WAAWA,CAAA,EAAG;IACV,MAAMwI,SAAS,GAAGnL,iBAAiB,CAAC5B,MAAM,CAACM,WAAW,CAAC,CAAC;IACxDoM,aAAa,CAAC,aAAa,CAAC;IAC5B,MAAMvF,GAAG,GAAGnH,MAAM,CAACqC,QAAQ,CAAC,CAAC2K,WAAW;IACxC,IAAID,SAAS,IAAI,OAAOE,mBAAmB,KAAK,WAAW,EAAE;MACzD,IAAI,CAAC7I,MAAM,GAAG+C,GAAG;MACjB,IAAI,CAAC2F,QAAQ,GAAG,IAAI,CAACI,uBAAuB,CAAC,CAAC;IAClD;EACJ;EACA;AACJ;AACA;AACA;EACIA,uBAAuBA,CAAA,EAAG;IACtB,MAAMJ,QAAQ,GAAG,IAAIG,mBAAmB,CAAEE,SAAS,IAAK;MACpD,MAAMnB,OAAO,GAAGmB,SAAS,CAACC,UAAU,CAAC,CAAC;MACtC,IAAIpB,OAAO,CAACtB,MAAM,KAAK,CAAC,EACpB;MACJ;MACA;MACA;MACA;MACA,MAAM2C,UAAU,GAAGrB,OAAO,CAACA,OAAO,CAACtB,MAAM,GAAG,CAAC,CAAC;MAC9C;MACA;MACA,MAAM4C,MAAM,GAAGD,UAAU,CAACE,OAAO,EAAErG,GAAG,IAAI,EAAE;MAC5C;MACA,IAAIoG,MAAM,CAAClF,UAAU,CAAC,OAAO,CAAC,IAAIkF,MAAM,CAAClF,UAAU,CAAC,OAAO,CAAC,EACxD;MACJ,MAAMoF,GAAG,GAAG,IAAI,CAACX,MAAM,CAACX,GAAG,CAACoB,MAAM,CAAC;MACnC,IAAI,CAACE,GAAG,EACJ;MACJ,IAAI,CAACA,GAAG,CAACC,QAAQ,IAAI,CAACD,GAAG,CAACE,qBAAqB,EAAE;QAC7CF,GAAG,CAACE,qBAAqB,GAAG,IAAI;QAChCC,uBAAuB,CAACL,MAAM,CAAC;MACnC;MACA,IAAIE,GAAG,CAACI,QAAQ,IAAI,CAACJ,GAAG,CAACK,qBAAqB,EAAE;QAC5CL,GAAG,CAACK,qBAAqB,GAAG,IAAI;QAChCC,kBAAkB,CAACR,MAAM,CAAC;MAC9B;IACJ,CAAC,CAAC;IACFR,QAAQ,CAACiB,OAAO,CAAC;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,OAAOnB,QAAQ;EACnB;EACAoB,aAAaA,CAACC,YAAY,EAAEC,aAAa,EAAEC,UAAU,EAAE;IACnD,IAAI,CAAC,IAAI,CAACvB,QAAQ,EACd;IACJ,MAAMwB,qBAAqB,GAAG;MAC1Bb,QAAQ,EAAEY,UAAU;MACpBT,QAAQ,EAAE,KAAK;MACfC,qBAAqB,EAAE,KAAK;MAC5BH,qBAAqB,EAAE;IAC3B,CAAC;IACD,IAAI,CAACb,MAAM,CAAC5B,GAAG,CAAChE,MAAM,CAACkH,YAAY,EAAE,IAAI,CAAC/J,MAAM,CAAC,CAACmD,IAAI,EAAE+G,qBAAqB,CAAC;EAClF;EACAC,eAAeA,CAACJ,YAAY,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACrB,QAAQ,EACd;IACJ,IAAI,CAACD,MAAM,CAAC2B,MAAM,CAACvH,MAAM,CAACkH,YAAY,EAAE,IAAI,CAAC/J,MAAM,CAAC,CAACmD,IAAI,CAAC;EAC9D;EACAkH,WAAWA,CAACC,WAAW,EAAEC,MAAM,EAAE;IAC7B,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EACd;IACJ,MAAM8B,WAAW,GAAG3H,MAAM,CAACyH,WAAW,EAAE,IAAI,CAACtK,MAAM,CAAC,CAACmD,IAAI;IACzD,MAAMiG,GAAG,GAAG,IAAI,CAACX,MAAM,CAACX,GAAG,CAAC0C,WAAW,CAAC;IACxC,IAAIpB,GAAG,EAAE;MACLA,GAAG,CAACI,QAAQ,GAAG,IAAI;MACnB,IAAI,CAACf,MAAM,CAAC5B,GAAG,CAAChE,MAAM,CAAC0H,MAAM,EAAE,IAAI,CAACvK,MAAM,CAAC,CAACmD,IAAI,EAAEiG,GAAG,CAAC;MACtD,IAAI,CAACX,MAAM,CAAC2B,MAAM,CAACI,WAAW,CAAC;IACnC;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAC/B,QAAQ,EACd;IACJ,IAAI,CAACA,QAAQ,CAACgC,UAAU,CAAC,CAAC;IAC1B,IAAI,CAACjC,MAAM,CAACkC,KAAK,CAAC,CAAC;EACvB;EACA,OAAOC,IAAI,YAAAC,yBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAyFtC,gBAAgB;EAAA;EACpH,OAAO/I,KAAK,kBAD8EnE,EAAE,CAAAI,kBAAA;IAAAgE,KAAA,EACY8I,gBAAgB;IAAA5I,OAAA,EAAhB4I,gBAAgB,CAAAoC,IAAA;IAAAjL,UAAA,EAAc;EAAM;AAChJ;AACA;EAAA,QAAAyE,SAAA,oBAAAA,SAAA,KAH8F9I,EAAE,CAAAyP,iBAAA,CAGJvC,gBAAgB,EAAc,CAAC;IAC/GoB,IAAI,EAAEzN,UAAU;IAChB6O,IAAI,EAAE,CAAC;MAAErL,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,SAAS4J,uBAAuBA,CAACpB,KAAK,EAAE;EACpC,MAAM8C,gBAAgB,GAAG/C,mBAAmB,CAACC,KAAK,CAAC;EACnDJ,OAAO,CAACmD,KAAK,CAACjP,mBAAmB,CAAC,IAAI,CAAC,iDAAiD,GAAGgP,gBAAgB,oDAAoD,GAC3J,qEAAqE,GACrE,iDAAiD,GACjD,4CAA4C,CAAC,CAAC;AACtD;AACA,SAASvB,kBAAkBA,CAACvB,KAAK,EAAE;EAC/B,MAAM8C,gBAAgB,GAAG/C,mBAAmB,CAACC,KAAK,CAAC;EACnDJ,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,+CAA+C,GAAGgP,gBAAgB,oDAAoD,GACxJ,qEAAqE,GACrE,0EAA0E,GAC1E,uDAAuD,CAAC,CAAC;AACjE;;AAEA;AACA,MAAME,mCAAmC,GAAG,IAAIC,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;AAC1F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,IAAIxP,cAAc,CAACuI,SAAS,GAAG,4BAA4B,GAAG,EAAE,CAAC;AACpG;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkH,qBAAqB,CAAC;EACxBrL,QAAQ,GAAGrE,MAAM,CAACqC,QAAQ,CAAC;EAC3B;AACJ;AACA;AACA;EACIsN,eAAe,GAAG,IAAI;EACtB;AACJ;AACA;EACIC,WAAW,GAAG,IAAIJ,GAAG,CAAC,CAAC;EACvBpL,MAAM,GAAG,IAAI,CAACC,QAAQ,CAAC2I,WAAW;EAClC6C,SAAS,GAAG,IAAIL,GAAG,CAACD,mCAAmC,CAAC;EACxDhL,WAAWA,CAAA,EAAG;IACVmI,aAAa,CAAC,yBAAyB,CAAC;IACxC,MAAMmD,SAAS,GAAG7P,MAAM,CAACyP,0BAA0B,EAAE;MAAEK,QAAQ,EAAE;IAAK,CAAC,CAAC;IACxE,IAAID,SAAS,EAAE;MACX,IAAI,CAACE,iBAAiB,CAACF,SAAS,CAAC;IACrC;EACJ;EACAE,iBAAiBA,CAACC,OAAO,EAAE;IACvB,IAAIvL,KAAK,CAACC,OAAO,CAACsL,OAAO,CAAC,EAAE;MACxBC,WAAW,CAACD,OAAO,EAAGzE,MAAM,IAAK;QAC7B,IAAI,CAACsE,SAAS,CAACK,GAAG,CAACzI,eAAe,CAAC8D,MAAM,CAAC,CAAC;MAC/C,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAACsE,SAAS,CAACK,GAAG,CAACzI,eAAe,CAACuI,OAAO,CAAC,CAAC;IAChD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIG,gBAAgBA,CAAChC,YAAY,EAAEC,aAAa,EAAE;IAC1C,IAAI,OAAOnK,YAAY,KAAK,WAAW,IAAIA,YAAY,EACnD;IACJ,MAAMmM,MAAM,GAAGnJ,MAAM,CAACkH,YAAY,EAAE,IAAI,CAAC/J,MAAM,CAAC;IAChD,IAAI,IAAI,CAACyL,SAAS,CAAC5D,GAAG,CAACmE,MAAM,CAACzI,QAAQ,CAAC,IAAI,IAAI,CAACiI,WAAW,CAAC3D,GAAG,CAACmE,MAAM,CAAC7E,MAAM,CAAC,EAC1E;IACJ;IACA,IAAI,CAACqE,WAAW,CAACM,GAAG,CAACE,MAAM,CAAC7E,MAAM,CAAC;IACnC;IACA;IACA;IACA;IACA,IAAI,CAACoE,eAAe,KAAK,IAAI,CAACU,oBAAoB,CAAC,CAAC;IACpD,IAAI,CAAC,IAAI,CAACV,eAAe,CAAC1D,GAAG,CAACmE,MAAM,CAAC7E,MAAM,CAAC,EAAE;MAC1CY,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,4DAA4D,GAAGiM,mBAAmB,CAAC8B,aAAa,CAAC,+CAA+C,GAClL,sFAAsF,GACtF,kFAAkF,GAClF,4CAA4C,GAC5C,kCAAkCgC,MAAM,CAAC7E,MAAM,IAAI,CAAC,CAAC;IAC7D;EACJ;EACA8E,oBAAoBA,CAAA,EAAG;IACnB,MAAMC,cAAc,GAAG,IAAId,GAAG,CAAC,CAAC;IAChC,MAAMe,KAAK,GAAG,IAAI,CAAClM,QAAQ,CAACmM,gBAAgB,CAAC,sBAAsB,CAAC;IACpE,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;MACtB,MAAM7I,GAAG,GAAGT,MAAM,CAACwJ,IAAI,CAAClJ,IAAI,EAAE,IAAI,CAACnD,MAAM,CAAC;MAC1CkM,cAAc,CAACJ,GAAG,CAACxI,GAAG,CAAC6D,MAAM,CAAC;IAClC;IACA,OAAO+E,cAAc;EACzB;EACAzB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACc,eAAe,EAAEZ,KAAK,CAAC,CAAC;IAC7B,IAAI,CAACa,WAAW,CAACb,KAAK,CAAC,CAAC;EAC5B;EACA,OAAOC,IAAI,YAAA0B,8BAAAxB,iBAAA;IAAA,YAAAA,iBAAA,IAAyFQ,qBAAqB;EAAA;EACzH,OAAO7L,KAAK,kBA1H8EnE,EAAE,CAAAI,kBAAA;IAAAgE,KAAA,EA0HY4L,qBAAqB;IAAA1L,OAAA,EAArB0L,qBAAqB,CAAAV,IAAA;IAAAjL,UAAA,EAAc;EAAM;AACrJ;AACA;EAAA,QAAAyE,SAAA,oBAAAA,SAAA,KA5H8F9I,EAAE,CAAAyP,iBAAA,CA4HJO,qBAAqB,EAAc,CAAC;IACpH1B,IAAI,EAAEzN,UAAU;IAChB6O,IAAI,EAAE,CAAC;MAAErL,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC;AACA;AACA;AACA;AACA,SAASkM,WAAWA,CAACU,KAAK,EAAEC,EAAE,EAAE;EAC5B,KAAK,IAAI9E,KAAK,IAAI6E,KAAK,EAAE;IACrBlM,KAAK,CAACC,OAAO,CAACoH,KAAK,CAAC,GAAGmE,WAAW,CAACnE,KAAK,EAAE8E,EAAE,CAAC,GAAGA,EAAE,CAAC9E,KAAK,CAAC;EAC7D;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+E,8BAA8B,GAAG,CAAC;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAI7Q,cAAc,CAAC,OAAOuI,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,+BAA+B,GAAG,EAAE,EAAE;EAC9HzE,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM,IAAIwL,GAAG,CAAC;AAC3B,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,kBAAkB,CAAC;EACrBC,eAAe,GAAGhR,MAAM,CAAC8Q,gBAAgB,CAAC;EAC1CzM,QAAQ,GAAGrE,MAAM,CAACqC,QAAQ,CAAC;EAC3B4O,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,oBAAoBA,CAACC,QAAQ,EAAEjK,GAAG,EAAEkK,MAAM,EAAEC,KAAK,EAAE;IAC/C,IAAI7I,SAAS,IACT,CAAC,IAAI,CAACyI,UAAU,IAChB,IAAI,CAACD,eAAe,CAACM,IAAI,IAAIT,8BAA8B,EAAE;MAC7D,IAAI,CAACI,UAAU,GAAG,IAAI;MACtB9E,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,kDAAkD,iEAAiE,GACrJ,GAAGwQ,8BAA8B,mCAAmC,GACpE,mEAAmE,GACnE,8EAA8E,CAAC,CAAC;IACxF;IACA,IAAI,IAAI,CAACG,eAAe,CAAC/E,GAAG,CAAC/E,GAAG,CAAC,EAAE;MAC/B;IACJ;IACA,IAAI,CAAC8J,eAAe,CAACd,GAAG,CAAChJ,GAAG,CAAC;IAC7B,MAAMqK,OAAO,GAAGJ,QAAQ,CAACK,aAAa,CAAC,MAAM,CAAC;IAC9CL,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC;IAC7CJ,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,MAAM,EAAErK,GAAG,CAAC;IAC3CiK,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,KAAK,EAAE,SAAS,CAAC;IAChDJ,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC;IACvD,IAAIF,KAAK,EAAE;MACPF,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,YAAY,EAAEF,KAAK,CAAC;IACvD;IACA,IAAID,MAAM,EAAE;MACRD,QAAQ,CAACM,YAAY,CAACF,OAAO,EAAE,aAAa,EAAEH,MAAM,CAAC;IACzD;IACAD,QAAQ,CAACO,WAAW,CAAC,IAAI,CAACrN,QAAQ,CAACsN,IAAI,EAAEJ,OAAO,CAAC;EACrD;EACA,OAAOvC,IAAI,YAAA4C,2BAAA1C,iBAAA;IAAA,YAAAA,iBAAA,IAAyF6B,kBAAkB;EAAA;EACtH,OAAOlN,KAAK,kBArN8EnE,EAAE,CAAAI,kBAAA;IAAAgE,KAAA,EAqNYiN,kBAAkB;IAAA/M,OAAA,EAAlB+M,kBAAkB,CAAA/B,IAAA;IAAAjL,UAAA,EAAc;EAAM;AAClJ;AACA;EAAA,QAAAyE,SAAA,oBAAAA,SAAA,KAvN8F9I,EAAE,CAAAyP,iBAAA,CAuNJ4B,kBAAkB,EAAc,CAAC;IACjH/C,IAAI,EAAEzN,UAAU;IAChB6O,IAAI,EAAE,CAAC;MAAErL,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8N,8BAA8B,GAAG,EAAE;AACzC;AACA;AACA;AACA;AACA,MAAMC,6BAA6B,GAAG,2BAA2B;AACjE;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG,mCAAmC;AAC3E;AACA;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,CAAC;AACrC;AACA;AACA;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;AACzC;AACA;AACA;AACA,MAAMC,0BAA0B,GAAG,GAAG;AACtC;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,GAAG,IAAI;AACtC;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,GAAG,IAAI;AACrC,MAAMC,yBAAyB,GAAG,IAAI;AACtC;AACA;AACA;AACA,MAAMC,uBAAuB,GAAG,EAAE;AAClC;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAI;AACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,IAAI;AAChC,MAAMC,oBAAoB,GAAG,KAAK;AAClC;AACA,MAAMC,gBAAgB,GAAG,CACrBjI,eAAe,EACfR,kBAAkB,EAClBT,oBAAoB,EACpByB,iBAAiB,CACpB;AACD;AACA;AACA;AACA,MAAM0H,wBAAwB,GAAG,EAAE;AACnC;AACA;AACA;AACA;AACA;AACA,IAAIC,6BAA6B,GAAG,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EACnBC,WAAW,GAAGhT,MAAM,CAACuI,YAAY,CAAC;EAClCD,MAAM,GAAG2K,aAAa,CAACjT,MAAM,CAACS,aAAa,CAAC,CAAC;EAC7C0Q,QAAQ,GAAGnR,MAAM,CAACU,SAAS,CAAC;EAC5BwS,UAAU,GAAGlT,MAAM,CAACW,UAAU,CAAC,CAACwS,aAAa;EAC7CC,QAAQ,GAAGpT,MAAM,CAACY,QAAQ,CAAC;EAC3B;EACA;EACAyS,WAAW;EACX;AACJ;AACA;AACA;AACA;AACA;EACIC,YAAY,GAAG,IAAI;EACnB;AACJ;AACA;AACA;AACA;EACI/G,KAAK;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgH,QAAQ;EACR;AACJ;AACA;AACA;EACIlC,KAAK;EACL;AACJ;AACA;AACA;EACI7H,KAAK;EACL;AACJ;AACA;AACA;EACIgK,MAAM;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;EACIhG,QAAQ,GAAG,KAAK;EAChB;AACJ;AACA;EACIvD,YAAY;EACZ;AACJ;AACA;EACIwJ,sBAAsB,GAAG,KAAK;EAC9B;AACJ;AACA;AACA;EACIC,IAAI,GAAG,KAAK;EACZ;AACJ;AACA;EACIC,WAAW;EACX;AACJ;AACA;AACA;EACIC,iBAAiB;EACjB;AACJ;AACA;AACA;AACA;AACA;EACI3M,GAAG;EACH;AACJ;AACA;AACA;AACA;AACA;EACIkK,MAAM;EACN7M,WAAWA,CAAA,EAAG;IACV,IAAIiE,SAAS,EAAE;MACX,IAAI,CAAC6K,WAAW,GAAG,IAAI,CAACD,QAAQ,CAAClH,GAAG,CAACU,gBAAgB,CAAC;MACtD;MACA;MACA,MAAMkH,UAAU,GAAG9T,MAAM,CAACa,UAAU,CAAC;MACrCiT,UAAU,CAACC,SAAS,CAAC,MAAM;QACvB,IAAI,CAAC,IAAI,CAACtG,QAAQ,IAAI,IAAI,CAAC6F,YAAY,KAAK,IAAI,EAAE;UAC9C,IAAI,CAACD,WAAW,CAAC9E,eAAe,CAAC,IAAI,CAAC+E,YAAY,CAAC;QACvD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAU,QAAQA,CAAA,EAAG;IACPjT,uBAAuB,CAAC,kBAAkB,CAAC;IAC3C,IAAIyH,SAAS,EAAE;MACX,MAAMyL,MAAM,GAAG,IAAI,CAACb,QAAQ,CAAClH,GAAG,CAAClL,MAAM,CAAC;MACxCkT,mBAAmB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC3H,KAAK,CAAC;MAC9C4H,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACZ,QAAQ,CAAC;MACxCa,sBAAsB,CAAC,IAAI,CAAC;MAC5B,IAAI,IAAI,CAACb,QAAQ,EAAE;QACfc,yBAAyB,CAAC,IAAI,CAAC;MACnC;MACAC,oBAAoB,CAAC,IAAI,CAAC;MAC1BC,gBAAgB,CAAC,IAAI,CAAC;MACtB,IAAI,IAAI,CAACZ,IAAI,EAAE;QACXa,yBAAyB,CAAC,IAAI,CAAC;QAC/B;QACA;QACAP,MAAM,CAACQ,iBAAiB,CAAC,MAAMC,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACxB,UAAU,EAAE,IAAI,CAAC/B,QAAQ,CAAC,CAAC;MACrG,CAAC,MACI;QACDwD,4BAA4B,CAAC,IAAI,CAAC;QAClC,IAAI,IAAI,CAACnB,MAAM,KAAKlK,SAAS,EAAE;UAC3BsL,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAACpB,MAAM,EAAE,QAAQ,CAAC;QACtD;QACA,IAAI,IAAI,CAAChK,KAAK,KAAKF,SAAS,EAAE;UAC1BsL,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAACpL,KAAK,EAAE,OAAO,CAAC;QACpD;QACA;QACA;QACAyK,MAAM,CAACQ,iBAAiB,CAAC,MAAMI,uBAAuB,CAAC,IAAI,EAAE,IAAI,CAAC3B,UAAU,EAAE,IAAI,CAAC/B,QAAQ,CAAC,CAAC;MACjG;MACA2D,uBAAuB,CAAC,IAAI,CAAC;MAC7B,IAAI,CAAC,IAAI,CAACvB,QAAQ,EAAE;QAChBwB,oBAAoB,CAAC,IAAI,CAAC;MAC9B;MACAC,sBAAsB,CAAC,IAAI,EAAE,IAAI,CAAChC,WAAW,CAAC;MAC9CiC,6BAA6B,CAAC,IAAI,CAAC1I,KAAK,EAAE,IAAI,CAACyG,WAAW,CAAC;MAC3DkC,6BAA6B,CAAC,IAAI,EAAE,IAAI,CAAClC,WAAW,CAAC;MACrDmC,iCAAiC,CAAC,IAAI,EAAE,IAAI,CAACnC,WAAW,CAAC;MACzDiB,MAAM,CAACQ,iBAAiB,CAAC,MAAM;QAC3B,IAAI,CAACpB,WAAW,CAACnF,aAAa,CAAC,IAAI,CAACkH,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC7I,KAAK,EAAE,IAAI,CAACkB,QAAQ,CAAC;MACrF,CAAC,CAAC;MACF,IAAI,IAAI,CAACA,QAAQ,EAAE;QACf,MAAM4H,OAAO,GAAG,IAAI,CAACjC,QAAQ,CAAClH,GAAG,CAACwD,qBAAqB,CAAC;QACxD2F,OAAO,CAAClF,gBAAgB,CAAC,IAAI,CAACiF,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC7I,KAAK,CAAC;QAC5D,IAAI,OAAOtI,YAAY,KAAK,WAAW,IAAI,CAACA,YAAY,EAAE;UACtD,MAAMqR,cAAc,GAAG,IAAI,CAAClC,QAAQ,CAAClH,GAAG,CAACjL,cAAc,CAAC;UACxDsU,gCAAgC,CAACD,cAAc,CAAC;QACpD;MACJ;IACJ;IACA,IAAI,IAAI,CAAC1B,WAAW,EAAE;MAClB,IAAI,CAAC4B,uBAAuB,CAAC,IAAI,CAACtC,UAAU,CAAC;IACjD;IACA,IAAI,CAACuC,iBAAiB,CAAC,CAAC;EAC5B;EACAA,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA,IAAI,IAAI,CAAC9B,IAAI,EAAE;MACX,IAAI,CAACtC,KAAK,KAAK,OAAO;IAC1B,CAAC,MACI;MACD,IAAI,CAACqE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClM,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC;MACrD,IAAI,CAACwK,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAClC,MAAM,CAACtI,QAAQ,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI,CAACwK,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACD,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACE,gBAAgB,CAAC,CAAC,CAAC;IAC/D;IACA;IACA,IAAI,CAACF,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC;IACvC;IACA;IACA,MAAMG,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACjD,IAAI,IAAI,CAACzE,KAAK,EAAE;MACZ,IAAI,IAAI,CAACsE,kBAAkB,CAAC,CAAC,KAAK,MAAM,EAAE;QACtC,IAAI,CAACD,gBAAgB,CAAC,OAAO,EAAE,QAAQ,GAAG,IAAI,CAACrE,KAAK,CAAC;MACzD,CAAC,MACI;QACD,IAAI,CAACqE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACrE,KAAK,CAAC;MAC9C;IACJ,CAAC,MACI;MACD,IAAI,IAAI,CAACkC,QAAQ,IACbzB,6BAA6B,CAACtK,IAAI,CAAC,IAAI,CAAC+L,QAAQ,CAAC,IACjD,IAAI,CAACoC,kBAAkB,CAAC,CAAC,KAAK,MAAM,EAAE;QACtC,IAAI,CAACD,gBAAgB,CAAC,OAAO,EAAE,aAAa,CAAC;MACjD;IACJ;IACA,IAAI,OAAOzR,YAAY,KAAK,WAAW,IAAIA,YAAY,IAAI,IAAI,CAACwJ,QAAQ,EAAE;MACtE,MAAMsI,kBAAkB,GAAG,IAAI,CAAC3C,QAAQ,CAAClH,GAAG,CAAC6E,kBAAkB,CAAC;MAChEgF,kBAAkB,CAAC7E,oBAAoB,CAAC,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACiE,eAAe,CAAC,CAAC,EAAES,eAAe,EAAE,IAAI,CAACxE,KAAK,CAAC;IAC/G;EACJ;EACA;EACA2E,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIzN,SAAS,EAAE;MACX0N,2BAA2B,CAAC,IAAI,EAAED,OAAO,EAAE,CACvC,UAAU,EACV,OAAO,EACP,QAAQ,EACR,UAAU,EACV,MAAM,EACN,SAAS,EACT,OAAO,EACP,cAAc,EACd,wBAAwB,CAC3B,CAAC;IACN;IACA,IAAIA,OAAO,CAAC,OAAO,CAAC,IAAI,CAACA,OAAO,CAAC,OAAO,CAAC,CAACE,aAAa,CAAC,CAAC,EAAE;MACvD,MAAMC,MAAM,GAAG,IAAI,CAAC9C,YAAY;MAChC,IAAI,CAACwC,kBAAkB,CAAC,IAAI,CAAC;MAC7B,IAAItN,SAAS,EAAE;QACX,MAAMmG,MAAM,GAAG,IAAI,CAAC2E,YAAY;QAChC,IAAI8C,MAAM,IAAIzH,MAAM,IAAIyH,MAAM,KAAKzH,MAAM,EAAE;UACvC,MAAMsF,MAAM,GAAG,IAAI,CAACb,QAAQ,CAAClH,GAAG,CAAClL,MAAM,CAAC;UACxCiT,MAAM,CAACQ,iBAAiB,CAAC,MAAM;YAC3B,IAAI,CAACpB,WAAW,CAAC5E,WAAW,CAAC2H,MAAM,EAAEzH,MAAM,CAAC;UAChD,CAAC,CAAC;QACN;MACJ;IACJ;IACA,IAAInG,SAAS,IACTyN,OAAO,CAAC,aAAa,CAAC,EAAEI,YAAY,IACpC,OAAOpS,YAAY,KAAK,WAAW,IACnC,CAACA,YAAY,EAAE;MACfqS,2BAA2B,CAAC,IAAI,EAAE,IAAI,CAACpD,UAAU,CAAC;IACtD;EACJ;EACAqD,eAAeA,CAACC,yBAAyB,EAAE;IACvC,IAAIC,eAAe,GAAGD,yBAAyB;IAC/C,IAAI,IAAI,CAACtM,YAAY,EAAE;MACnBuM,eAAe,CAACvM,YAAY,GAAG,IAAI,CAACA,YAAY;IACpD;IACA,OAAO,IAAI,CAAC8I,WAAW,CAACyD,eAAe,CAAC;EAC5C;EACAd,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAAClI,QAAQ,IAAI,IAAI,CAACgG,OAAO,KAAKnK,SAAS,EAAE;MAC9C,OAAO,IAAI,CAACmK,OAAO;IACvB;IACA,OAAO,IAAI,CAAChG,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC3C;EACAmI,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnI,QAAQ,GAAG,MAAM,GAAG,MAAM;EAC1C;EACA2H,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC9B,YAAY,EAAE;MACpB,MAAMoD,SAAS,GAAG;QAAExP,GAAG,EAAE,IAAI,CAACqF;MAAM,CAAC;MACrC;MACA,IAAI,CAAC+G,YAAY,GAAG,IAAI,CAACiD,eAAe,CAACG,SAAS,CAAC;IACvD;IACA,OAAO,IAAI,CAACpD,YAAY;EAC5B;EACAqD,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,WAAW,GAAG9E,6BAA6B,CAACtK,IAAI,CAAC,IAAI,CAAC+L,QAAQ,CAAC;IACrE,MAAMsD,SAAS,GAAG,IAAI,CAACtD,QAAQ,CAC1BuD,KAAK,CAAC,GAAG,CAAC,CACVC,MAAM,CAAE7P,GAAG,IAAKA,GAAG,KAAK,EAAE,CAAC,CAC3B8P,GAAG,CAAEC,MAAM,IAAK;MACjBA,MAAM,GAAGA,MAAM,CAAClP,IAAI,CAAC,CAAC;MACtB,MAAMyB,KAAK,GAAGoN,WAAW,GAAGM,UAAU,CAACD,MAAM,CAAC,GAAGC,UAAU,CAACD,MAAM,CAAC,GAAG,IAAI,CAACzN,KAAK;MAChF,OAAO,GAAG,IAAI,CAAC+M,eAAe,CAAC;QAAErP,GAAG,EAAE,IAAI,CAACqF,KAAK;QAAE/C;MAAM,CAAC,CAAC,IAAIyN,MAAM,EAAE;IAC1E,CAAC,CAAC;IACF,OAAOJ,SAAS,CAAC1N,IAAI,CAAC,IAAI,CAAC;EAC/B;EACAgO,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAAC9F,KAAK,EAAE;MACZ,OAAO,IAAI,CAAC+F,mBAAmB,CAAC,CAAC;IACrC,CAAC,MACI;MACD,OAAO,IAAI,CAACC,cAAc,CAAC,CAAC;IAChC;EACJ;EACAD,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEE;IAAY,CAAC,GAAG,IAAI,CAAChP,MAAM;IACnC,IAAIiP,mBAAmB,GAAGD,WAAW;IACrC,IAAI,IAAI,CAACjG,KAAK,EAAEtJ,IAAI,CAAC,CAAC,KAAK,OAAO,EAAE;MAChC;MACA;MACAwP,mBAAmB,GAAGD,WAAW,CAACP,MAAM,CAAES,EAAE,IAAKA,EAAE,IAAIrF,0BAA0B,CAAC;IACtF;IACA,MAAM0E,SAAS,GAAGU,mBAAmB,CAACP,GAAG,CAAEQ,EAAE,IAAK,GAAG,IAAI,CAACjB,eAAe,CAAC;MAAErP,GAAG,EAAE,IAAI,CAACqF,KAAK;MAAE/C,KAAK,EAAEgO;IAAG,CAAC,CAAC,IAAIA,EAAE,GAAG,CAAC;IACnH,OAAOX,SAAS,CAAC1N,IAAI,CAAC,IAAI,CAAC;EAC/B;EACA2M,kBAAkBA,CAAC2B,cAAc,GAAG,KAAK,EAAE;IACvC,IAAIA,cAAc,EAAE;MAChB;MACA;MACA,IAAI,CAACnE,YAAY,GAAG,IAAI;IAC5B;IACA,MAAMnF,YAAY,GAAG,IAAI,CAACiH,eAAe,CAAC,CAAC;IAC3C,IAAI,CAACM,gBAAgB,CAAC,KAAK,EAAEvH,YAAY,CAAC;IAC1C,IAAI0H,eAAe,GAAGvM,SAAS;IAC/B,IAAI,IAAI,CAACiK,QAAQ,EAAE;MACfsC,eAAe,GAAG,IAAI,CAACc,kBAAkB,CAAC,CAAC;IAC/C,CAAC,MACI,IAAI,IAAI,CAACe,6BAA6B,CAAC,CAAC,EAAE;MAC3C7B,eAAe,GAAG,IAAI,CAACsB,kBAAkB,CAAC,CAAC;IAC/C;IACA,IAAItB,eAAe,EAAE;MACjB,IAAI,CAACH,gBAAgB,CAAC,QAAQ,EAAEG,eAAe,CAAC;IACpD;IACA,OAAOA,eAAe;EAC1B;EACAwB,cAAcA,CAAA,EAAG;IACb,MAAMR,SAAS,GAAG3E,0BAA0B,CAAC8E,GAAG,CAAEW,UAAU,IAAK,GAAG,IAAI,CAACpB,eAAe,CAAC;MACrFrP,GAAG,EAAE,IAAI,CAACqF,KAAK;MACf/C,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGmO;IACxB,CAAC,CAAC,IAAIA,UAAU,GAAG,CAAC;IACpB,OAAOd,SAAS,CAAC1N,IAAI,CAAC,IAAI,CAAC;EAC/B;EACAuO,6BAA6BA,CAAA,EAAG;IAC5B,IAAIE,cAAc,GAAG,KAAK;IAC1B,IAAI,CAAC,IAAI,CAACvG,KAAK,EAAE;MACbuG,cAAc,GACV,IAAI,CAACpO,KAAK,GAAG8I,wBAAwB,IAAI,IAAI,CAACkB,MAAM,GAAGjB,yBAAyB;IACxF;IACA,OAAQ,CAAC,IAAI,CAACmB,sBAAsB,IAChC,CAAC,IAAI,CAACtC,MAAM,IACZ,IAAI,CAAC4B,WAAW,KAAK3K,eAAe,IACpC,CAACuP,cAAc;EACvB;EACA;AACJ;AACA;AACA;AACA;EACIC,mBAAmBA,CAACC,gBAAgB,EAAE;IAClC,MAAM;MAAEC;IAAsB,CAAC,GAAG,IAAI,CAACzP,MAAM;IAC7C,IAAIwP,gBAAgB,KAAK,IAAI,EAAE;MAC3B,OAAO,OAAO,IAAI,CAACvB,eAAe,CAAC;QAC/BrP,GAAG,EAAE,IAAI,CAACqF,KAAK;QACf/C,KAAK,EAAEuO,qBAAqB;QAC5BtO,aAAa,EAAE;MACnB,CAAC,CAAC,GAAG;IACT,CAAC,MACI,IAAI,OAAOqO,gBAAgB,KAAK,QAAQ,EAAE;MAC3C,OAAO,OAAOA,gBAAgB,GAAG;IACrC;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,qBAAqBA,CAACnE,iBAAiB,EAAE;IACrC,IAAI,CAACA,iBAAiB,IAAI,CAACA,iBAAiB,CAACoE,cAAc,CAAC,MAAM,CAAC,EAAE;MACjE,OAAO,IAAI;IACf;IACA,OAAOC,OAAO,CAACrE,iBAAiB,CAACsE,IAAI,CAAC;EAC1C;EACA3C,uBAAuBA,CAAChI,GAAG,EAAE;IACzB,MAAM4K,QAAQ,GAAGA,CAAA,KAAM;MACnB,MAAMC,iBAAiB,GAAG,IAAI,CAACjF,QAAQ,CAAClH,GAAG,CAAC9K,iBAAiB,CAAC;MAC9DkX,oBAAoB,CAAC,CAAC;MACtBC,qBAAqB,CAAC,CAAC;MACvB,IAAI,CAAC3E,WAAW,GAAG,KAAK;MACxByE,iBAAiB,CAACG,YAAY,CAAC,CAAC;IACpC,CAAC;IACD,MAAMF,oBAAoB,GAAG,IAAI,CAACnH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,MAAM,EAAE4K,QAAQ,CAAC;IACxE,MAAMG,qBAAqB,GAAG,IAAI,CAACpH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,OAAO,EAAE4K,QAAQ,CAAC;IAC1EM,yBAAyB,CAAClL,GAAG,EAAE4K,QAAQ,CAAC;EAC5C;EACA1C,gBAAgBA,CAAC/L,IAAI,EAAEmC,KAAK,EAAE;IAC1B,IAAI,CAACqF,QAAQ,CAACM,YAAY,CAAC,IAAI,CAACyB,UAAU,EAAEvJ,IAAI,EAAEmC,KAAK,CAAC;EAC5D;EACA,OAAOkD,IAAI,YAAA2J,yBAAAzJ,iBAAA;IAAA,YAAAA,iBAAA,IAAyF6D,gBAAgB;EAAA;EACpH,OAAO6F,IAAI,kBApxB+ElZ,EAAE,CAAAmZ,iBAAA;IAAA7K,IAAA,EAoxBJ+E,gBAAgB;IAAA+F,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QApxBdxZ,EAAE,CAAA0Z,WAAA,aAAAD,GAAA,CAAAxF,IAAA,GAoxBG,UAAU,GAAG,IAAL,CAAC,UAAAwF,GAAA,CAAAxF,IAAA,GAAT,MAAM,GAAG,IAAD,CAAC,WAAAwF,GAAA,CAAAxF,IAAA,GAAT,MAAM,GAAG,IAAD,CAAC,UAAAwF,GAAA,CAAAxF,IAAA,GAAT,GAAG,GAAG,IAAE,CAAC,oBAAAwF,GAAA,CAAAvF,WAAA,GAAF,OAAO,GAAG,IAAT,CAAC,wBAAAuF,GAAA,CAAAvF,WAAA,GAAF,SAAS,GAAG,IAAX,CAAC,sBAAAuF,GAAA,CAAAvF,WAAA,GAAF,WAAW,GAAG,IAAb,CAAC,qBAAAuF,GAAA,CAAAvF,WAAA,GAAFuF,GAAA,CAAAtB,mBAAA,CAAAsB,GAAA,CAAAvF,WAA+B,CAAC,GAAG,IAAlC,CAAC,WAAAuF,GAAA,CAAAvF,WAAA,IAADuF,GAAA,CAAAnB,qBAAA,CAAAmB,GAAA,CAAAtF,iBAAuC,CAAC,GAAG,YAAY,GAAG,IAA1D,CAAC;MAAA;IAAA;IAAAwF,MAAA;MAAA9M,KAAA,wBAAkF+M,aAAa;MAAA/F,QAAA;MAAAlC,KAAA;MAAA7H,KAAA,wBAAmErI,eAAe;MAAAqS,MAAA,0BAAgCrS,eAAe;MAAAsS,OAAA;MAAAhG,QAAA,8BAA0DvM,gBAAgB;MAAAgJ,YAAA;MAAAwJ,sBAAA,0DAA8GxS,gBAAgB;MAAAyS,IAAA,sBAA0BzS,gBAAgB;MAAA0S,WAAA,oCAA+C2F,qBAAqB;MAAA1F,iBAAA;MAAA3M,GAAA;MAAAkK,MAAA;IAAA;IAAAoI,QAAA,GApxBpiB9Z,EAAE,CAAA+Z,oBAAA;EAAA;AAqxBhG;AACA;EAAA,QAAAjR,SAAA,oBAAAA,SAAA,KAtxB8F9I,EAAE,CAAAyP,iBAAA,CAsxBJ4D,gBAAgB,EAAc,CAAC;IAC/G/E,IAAI,EAAEtM,SAAS;IACf0N,IAAI,EAAE,CAAC;MACCsK,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACF,kBAAkB,EAAE,0BAA0B;QAC9C,eAAe,EAAE,sBAAsB;QACvC,gBAAgB,EAAE,sBAAsB;QACxC,eAAe,EAAE,mBAAmB;QACpC,yBAAyB,EAAE,8BAA8B;QACzD,6BAA6B,EAAE,gCAAgC;QAC/D,2BAA2B,EAAE,kCAAkC;QAC/D,0BAA0B,EAAE,uDAAuD;QACnF,gBAAgB,EAAE,mEAAmEnH,uBAAuB;MAChH;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEjG,KAAK,EAAE,CAAC;MAChDyB,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEwK,QAAQ,EAAE,IAAI;QAAEC,SAAS,EAAEP;MAAc,CAAC;IACvD,CAAC,CAAC;IAAE/F,QAAQ,EAAE,CAAC;MACXvF,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAE4P,KAAK,EAAE,CAAC;MACRrD,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAE+H,KAAK,EAAE,CAAC;MACRwE,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAE1Y;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEqS,MAAM,EAAE,CAAC;MACTxF,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAE1Y;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEsS,OAAO,EAAE,CAAC;MACVzF,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAEgM,QAAQ,EAAE,CAAC;MACXO,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAE3Y;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEgJ,YAAY,EAAE,CAAC;MACf8D,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAEiS,sBAAsB,EAAE,CAAC;MACzB1F,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAE3Y;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyS,IAAI,EAAE,CAAC;MACP3F,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAE3Y;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0S,WAAW,EAAE,CAAC;MACd5F,IAAI,EAAEvM,KAAK;MACX2N,IAAI,EAAE,CAAC;QAAEyK,SAAS,EAAEN;MAAsB,CAAC;IAC/C,CAAC,CAAC;IAAE1F,iBAAiB,EAAE,CAAC;MACpB7F,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAEyF,GAAG,EAAE,CAAC;MACN8G,IAAI,EAAEvM;IACV,CAAC,CAAC;IAAE2P,MAAM,EAAE,CAAC;MACTpD,IAAI,EAAEvM;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASwR,aAAaA,CAAC3K,MAAM,EAAE;EAC3B,IAAIwR,iBAAiB,GAAG,CAAC,CAAC;EAC1B,IAAIxR,MAAM,CAACgP,WAAW,EAAE;IACpBwC,iBAAiB,CAACxC,WAAW,GAAGhP,MAAM,CAACgP,WAAW,CAACyC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;EAC5E;EACA,OAAOlO,MAAM,CAACmO,MAAM,CAAC,CAAC,CAAC,EAAE5Y,sBAAsB,EAAEgH,MAAM,EAAEwR,iBAAiB,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,SAAS1F,sBAAsBA,CAAC+F,GAAG,EAAE;EACjC,IAAIA,GAAG,CAACjT,GAAG,EAAE;IACT,MAAM,IAAI/G,aAAa,CAAC,IAAI,CAAC,4CAA4C,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,6CAA6C,GACnJ,0DAA0D,GAC1D,sFAAsF,GACtF,mDAAmD,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA,SAAS8H,yBAAyBA,CAAC8F,GAAG,EAAE;EACpC,IAAIA,GAAG,CAAC/I,MAAM,EAAE;IACZ,MAAM,IAAIjR,aAAa,CAAC,IAAI,CAAC,+CAA+C,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,mDAAmD,GAC5J,0DAA0D,GAC1D,8EAA8E,GAC9E,oEAAoE,CAAC;EAC7E;AACJ;AACA;AACA;AACA;AACA,SAAS+H,oBAAoBA,CAAC6F,GAAG,EAAE;EAC/B,IAAI5N,KAAK,GAAG4N,GAAG,CAAC5N,KAAK,CAACxE,IAAI,CAAC,CAAC;EAC5B,IAAIwE,KAAK,CAACnE,UAAU,CAAC,OAAO,CAAC,EAAE;IAC3B,IAAImE,KAAK,CAAC7B,MAAM,GAAGmH,8BAA8B,EAAE;MAC/CtF,KAAK,GAAGA,KAAK,CAAC6N,SAAS,CAAC,CAAC,EAAEvI,8BAA8B,CAAC,GAAG,KAAK;IACtE;IACA,MAAM,IAAI1R,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,EAAE,KAAK,CAAC,wCAAwC,GAC/I,IAAIA,KAAK,+DAA+D,GACxE,uEAAuE,GACvE,uEAAuE,CAAC;EAChF;AACJ;AACA;AACA;AACA;AACA,SAASwI,oBAAoBA,CAACoF,GAAG,EAAE;EAC/B,IAAI9I,KAAK,GAAG8I,GAAG,CAAC9I,KAAK;EACrB,IAAIA,KAAK,EAAEgJ,KAAK,CAAC,mBAAmB,CAAC,EAAE;IACnC,MAAM,IAAIla,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,EAAE,KAAK,CAAC,2CAA2C,GAClJ,4FAA4F,GAC5F,kFAAkF,GAClF,+FAA+F,CAAC;EACxG;AACJ;AACA,SAASyI,sBAAsBA,CAACmF,GAAG,EAAEnH,WAAW,EAAE;EAC9CsH,2CAA2C,CAACH,GAAG,CAAC;EAChDI,wCAAwC,CAACJ,GAAG,EAAEnH,WAAW,CAAC;EAC1DwH,wBAAwB,CAACL,GAAG,CAAC;AACjC;AACA;AACA;AACA;AACA,SAASG,2CAA2CA,CAACH,GAAG,EAAE;EACtD,IAAIA,GAAG,CAACtG,iBAAiB,IAAI,CAACsG,GAAG,CAACvG,WAAW,EAAE;IAC3C,MAAM,IAAIzT,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,EAAE,KAAK,CAAC,sDAAsD,GAC7J,iFAAiF,CAAC;EAC1F;AACJ;AACA;AACA;AACA;AACA;AACA,SAASgO,wCAAwCA,CAACJ,GAAG,EAAEnH,WAAW,EAAE;EAChE,IAAImH,GAAG,CAACvG,WAAW,KAAK,IAAI,IAAIZ,WAAW,KAAK3K,eAAe,EAAE;IAC7D,MAAM,IAAIlI,aAAa,CAAC,IAAI,CAAC,iDAAiD,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,oDAAoD,GAC/J,sEAAsE,GACtE,6FAA6F,GAC7F,uFAAuF,CAAC;EAChG;AACJ;AACA;AACA;AACA;AACA,SAASiO,wBAAwBA,CAACL,GAAG,EAAE;EACnC,IAAIA,GAAG,CAACvG,WAAW,IACf,OAAOuG,GAAG,CAACvG,WAAW,KAAK,QAAQ,IACnCuG,GAAG,CAACvG,WAAW,CAACxL,UAAU,CAAC,OAAO,CAAC,EAAE;IACrC,IAAI+R,GAAG,CAACvG,WAAW,CAAClJ,MAAM,GAAGiI,oBAAoB,EAAE;MAC/C,MAAM,IAAIxS,aAAa,CAAC,IAAI,CAAC,8CAA8C,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,sEAAsE,GAC9K,QAAQoG,oBAAoB,0EAA0E,GACtG,qGAAqG,GACrG,iCAAiC,CAAC;IAC1C;IACA,IAAIwH,GAAG,CAACvG,WAAW,CAAClJ,MAAM,GAAGgI,mBAAmB,EAAE;MAC9CvG,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,8CAA8C,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,sEAAsE,GACvL,QAAQmG,mBAAmB,iEAAiE,GAC5F,+GAA+G,GAC/G,0CAA0C,CAAC,CAAC;IACpD;EACJ;AACJ;AACA;AACA;AACA;AACA,SAAS6B,gBAAgBA,CAAC4F,GAAG,EAAE;EAC3B,MAAM5N,KAAK,GAAG4N,GAAG,CAAC5N,KAAK,CAACxE,IAAI,CAAC,CAAC;EAC9B,IAAIwE,KAAK,CAACnE,UAAU,CAAC,OAAO,CAAC,EAAE;IAC3B,MAAM,IAAIjI,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,qCAAqCA,KAAK,KAAK,GAC/I,iEAAiE,GACjE,uEAAuE,GACvE,sEAAsE,CAAC;EAC/E;AACJ;AACA;AACA;AACA;AACA,SAAS2H,mBAAmBA,CAACiG,GAAG,EAAExQ,IAAI,EAAEmC,KAAK,EAAE;EAC3C,MAAMhE,QAAQ,GAAG,OAAOgE,KAAK,KAAK,QAAQ;EAC1C,MAAM2O,aAAa,GAAG3S,QAAQ,IAAIgE,KAAK,CAAC/D,IAAI,CAAC,CAAC,KAAK,EAAE;EACrD,IAAI,CAACD,QAAQ,IAAI2S,aAAa,EAAE;IAC5B,MAAM,IAAIta,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,MAAM5C,IAAI,0BAA0B,GACpI,MAAMmC,KAAK,2DAA2D,CAAC;EAC/E;AACJ;AACA;AACA;AACA;AACA,SAASqI,mBAAmBA,CAACgG,GAAG,EAAErO,KAAK,EAAE;EACrC,IAAIA,KAAK,IAAI,IAAI,EACb;EACJoI,mBAAmB,CAACiG,GAAG,EAAE,UAAU,EAAErO,KAAK,CAAC;EAC3C,MAAM4O,SAAS,GAAG5O,KAAK;EACvB,MAAM6O,sBAAsB,GAAG7I,6BAA6B,CAACtK,IAAI,CAACkT,SAAS,CAAC;EAC5E,MAAME,wBAAwB,GAAG7I,+BAA+B,CAACvK,IAAI,CAACkT,SAAS,CAAC;EAChF,IAAIE,wBAAwB,EAAE;IAC1BC,qBAAqB,CAACV,GAAG,EAAEO,SAAS,CAAC;EACzC;EACA,MAAMI,aAAa,GAAGH,sBAAsB,IAAIC,wBAAwB;EACxE,IAAI,CAACE,aAAa,EAAE;IAChB,MAAM,IAAI3a,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,yCAAyCT,KAAK,OAAO,GACrJ,qFAAqF,GACrF,yEAAyE,CAAC;EAClF;AACJ;AACA,SAAS+O,qBAAqBA,CAACV,GAAG,EAAErO,KAAK,EAAE;EACvC,MAAMiP,eAAe,GAAGjP,KAAK,CACxBgL,KAAK,CAAC,GAAG,CAAC,CACVkE,KAAK,CAAEC,GAAG,IAAKA,GAAG,KAAK,EAAE,IAAI/D,UAAU,CAAC+D,GAAG,CAAC,IAAIjJ,2BAA2B,CAAC;EACjF,IAAI,CAAC+I,eAAe,EAAE;IAClB,MAAM,IAAI5a,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,0DAA0D,GAC1J,KAAKT,KAAK,mEAAmE,GAC7E,GAAGmG,8BAA8B,uCAAuC,GACxE,GAAGD,2BAA2B,8DAA8D,GAC5F,gBAAgBC,8BAA8B,uCAAuC,GACrF,0FAA0F,GAC1F,GAAGD,2BAA2B,oEAAoE,CAAC;EAC3G;AACJ;AACA;AACA;AACA;AACA;AACA,SAASkJ,wBAAwBA,CAACf,GAAG,EAAEgB,SAAS,EAAE;EAC9C,IAAIC,MAAM;EACV,IAAID,SAAS,KAAK,OAAO,IAAIA,SAAS,KAAK,QAAQ,EAAE;IACjDC,MAAM,GACF,cAAcD,SAAS,6CAA6C,GAChE,4EAA4E;EACxF,CAAC,MACI;IACDC,MAAM,GACF,kBAAkBD,SAAS,4CAA4C,GACnE,mEAAmE;EAC/E;EACA,OAAO,IAAIhb,aAAa,CAAC,IAAI,CAAC,gDAAgD,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,MAAM4O,SAAS,uCAAuC,GACjK,uEAAuEC,MAAM,GAAG,GAChF,gCAAgCD,SAAS,uBAAuB,GAChE,2EAA2E,CAAC;AACpF;AACA;AACA;AACA;AACA,SAASjF,2BAA2BA,CAACiE,GAAG,EAAElE,OAAO,EAAEoD,MAAM,EAAE;EACvDA,MAAM,CAACgC,OAAO,CAAE1K,KAAK,IAAK;IACtB,MAAM2K,SAAS,GAAGrF,OAAO,CAACgC,cAAc,CAACtH,KAAK,CAAC;IAC/C,IAAI2K,SAAS,IAAI,CAACrF,OAAO,CAACtF,KAAK,CAAC,CAACwF,aAAa,CAAC,CAAC,EAAE;MAC9C,IAAIxF,KAAK,KAAK,OAAO,EAAE;QACnB;QACA;QACA;QACA;QACAwJ,GAAG,GAAG;UAAE5N,KAAK,EAAE0J,OAAO,CAACtF,KAAK,CAAC,CAAC4K;QAAc,CAAC;MACjD;MACA,MAAML,wBAAwB,CAACf,GAAG,EAAExJ,KAAK,CAAC;IAC9C;EACJ,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,SAASiE,qBAAqBA,CAACuF,GAAG,EAAEqB,UAAU,EAAEL,SAAS,EAAE;EACvD,MAAMM,WAAW,GAAG,OAAOD,UAAU,KAAK,QAAQ,IAAIA,UAAU,GAAG,CAAC;EACpE,MAAME,WAAW,GAAG,OAAOF,UAAU,KAAK,QAAQ,IAAI,OAAO,CAAChU,IAAI,CAACgU,UAAU,CAACzT,IAAI,CAAC,CAAC,CAAC,IAAI4T,QAAQ,CAACH,UAAU,CAAC,GAAG,CAAC;EACjH,IAAI,CAACC,WAAW,IAAI,CAACC,WAAW,EAAE;IAC9B,MAAM,IAAIvb,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,MAAM4O,SAAS,2BAA2B,GAC1I,0BAA0BA,SAAS,gCAAgC,CAAC;EAC5E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAAStG,uBAAuBA,CAACsF,GAAG,EAAE3M,GAAG,EAAE2D,QAAQ,EAAE;EACjD,MAAMiH,QAAQ,GAAGA,CAAA,KAAM;IACnBE,oBAAoB,CAAC,CAAC;IACtBC,qBAAqB,CAAC,CAAC;IACvB,MAAMqD,aAAa,GAAGxX,MAAM,CAACyX,gBAAgB,CAACrO,GAAG,CAAC;IAClD,IAAIsO,aAAa,GAAG5E,UAAU,CAAC0E,aAAa,CAACG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACvE,IAAIC,cAAc,GAAG9E,UAAU,CAAC0E,aAAa,CAACG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;IACzE,MAAME,SAAS,GAAGL,aAAa,CAACG,gBAAgB,CAAC,YAAY,CAAC;IAC9D,IAAIE,SAAS,KAAK,YAAY,EAAE;MAC5B,MAAMC,UAAU,GAAGN,aAAa,CAACG,gBAAgB,CAAC,aAAa,CAAC;MAChE,MAAMI,YAAY,GAAGP,aAAa,CAACG,gBAAgB,CAAC,eAAe,CAAC;MACpE,MAAMK,aAAa,GAAGR,aAAa,CAACG,gBAAgB,CAAC,gBAAgB,CAAC;MACtE,MAAMM,WAAW,GAAGT,aAAa,CAACG,gBAAgB,CAAC,cAAc,CAAC;MAClED,aAAa,IAAI5E,UAAU,CAACiF,YAAY,CAAC,GAAGjF,UAAU,CAACmF,WAAW,CAAC;MACnEL,cAAc,IAAI9E,UAAU,CAACgF,UAAU,CAAC,GAAGhF,UAAU,CAACkF,aAAa,CAAC;IACxE;IACA,MAAME,mBAAmB,GAAGR,aAAa,GAAGE,cAAc;IAC1D,MAAMO,yBAAyB,GAAGT,aAAa,KAAK,CAAC,IAAIE,cAAc,KAAK,CAAC;IAC7E,MAAMQ,cAAc,GAAGhP,GAAG,CAACiP,YAAY;IACvC,MAAMC,eAAe,GAAGlP,GAAG,CAACmP,aAAa;IACzC,MAAMC,oBAAoB,GAAGJ,cAAc,GAAGE,eAAe;IAC7D,MAAMG,aAAa,GAAG1C,GAAG,CAAC3Q,KAAK;IAC/B,MAAMsT,cAAc,GAAG3C,GAAG,CAAC3G,MAAM;IACjC,MAAMuJ,mBAAmB,GAAGF,aAAa,GAAGC,cAAc;IAC1D;IACA;IACA;IACA;IACA;IACA,MAAME,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CAACH,mBAAmB,GAAGH,oBAAoB,CAAC,GAAGxK,sBAAsB;IAC1G,MAAM+K,iBAAiB,GAAGZ,yBAAyB,IAC/CU,IAAI,CAACC,GAAG,CAACN,oBAAoB,GAAGN,mBAAmB,CAAC,GAAGlK,sBAAsB;IACjF,IAAI4K,oBAAoB,EAAE;MACtB7Q,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,sCAAsC,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,gDAAgD,GACzJ,iEAAiE,GACjE,2BAA2BiQ,cAAc,OAAOE,eAAe,IAAI,GACnE,kBAAkBU,KAAK,CAACR,oBAAoB,CAAC,6CAA6C,GAC1F,GAAGC,aAAa,OAAOC,cAAc,oBAAoBM,KAAK,CAACL,mBAAmB,CAAC,KAAK,GACxF,wDAAwD,CAAC,CAAC;IAClE,CAAC,MACI,IAAII,iBAAiB,EAAE;MACxBhR,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,sCAAsC,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,0CAA0C,GACnJ,qDAAqD,GACrD,2BAA2BiQ,cAAc,OAAOE,eAAe,IAAI,GACnE,kBAAkBU,KAAK,CAACR,oBAAoB,CAAC,4BAA4B,GACzE,GAAGd,aAAa,OAAOE,cAAc,mBAAmB,GACxD,GAAGoB,KAAK,CAACd,mBAAmB,CAAC,oDAAoD,GACjF,sEAAsE,GACtE,mEAAmE,GACnE,uEAAuE,GACvE,aAAa,CAAC,CAAC;IACvB,CAAC,MACI,IAAI,CAACnC,GAAG,CAAC5G,QAAQ,IAAIgJ,yBAAyB,EAAE;MACjD;MACA,MAAMc,gBAAgB,GAAGpL,8BAA8B,GAAG6J,aAAa;MACvE,MAAMwB,iBAAiB,GAAGrL,8BAA8B,GAAG+J,cAAc;MACzE,MAAMuB,cAAc,GAAGf,cAAc,GAAGa,gBAAgB,IAAIhL,yBAAyB;MACrF,MAAMmL,eAAe,GAAGd,eAAe,GAAGY,iBAAiB,IAAIjL,yBAAyB;MACxF,IAAIkL,cAAc,IAAIC,eAAe,EAAE;QACnCrR,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,wCAAwC,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,wCAAwC,GACnJ,yBAAyB,GACzB,0BAA0BuP,aAAa,OAAOE,cAAc,KAAK,GACjE,2BAA2BQ,cAAc,OAAOE,eAAe,KAAK,GACpE,uCAAuCW,gBAAgB,OAAOC,iBAAiB,KAAK,GACpF,mFAAmF,GACnF,GAAGrL,8BAA8B,8CAA8C,GAC/E,0DAA0D,CAAC,CAAC;MACpE;IACJ;EACJ,CAAC;EACD,MAAMqG,oBAAoB,GAAGnH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,MAAM,EAAE4K,QAAQ,CAAC;EACnE;EACA;EACA;EACA;EACA,MAAMG,qBAAqB,GAAGpH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,OAAO,EAAE,MAAM;IAC9D8K,oBAAoB,CAAC,CAAC;IACtBC,qBAAqB,CAAC,CAAC;EAC3B,CAAC,CAAC;EACFG,yBAAyB,CAAClL,GAAG,EAAE4K,QAAQ,CAAC;AAC5C;AACA;AACA;AACA;AACA,SAASzD,4BAA4BA,CAACwF,GAAG,EAAE;EACvC,IAAIsD,iBAAiB,GAAG,EAAE;EAC1B,IAAItD,GAAG,CAAC3Q,KAAK,KAAKF,SAAS,EACvBmU,iBAAiB,CAACjT,IAAI,CAAC,OAAO,CAAC;EACnC,IAAI2P,GAAG,CAAC3G,MAAM,KAAKlK,SAAS,EACxBmU,iBAAiB,CAACjT,IAAI,CAAC,QAAQ,CAAC;EACpC,IAAIiT,iBAAiB,CAAC/S,MAAM,GAAG,CAAC,EAAE;IAC9B,MAAM,IAAIvK,aAAa,CAAC,IAAI,CAAC,+CAA+C,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,6BAA6B,GACtI,gBAAgBkR,iBAAiB,CAACzG,GAAG,CAAE0G,IAAI,IAAK,IAAIA,IAAI,GAAG,CAAC,CAACvU,IAAI,CAAC,IAAI,CAAC,IAAI,GAC3E,sFAAsF,GACtF,mFAAmF,GACnF,0CAA0C,CAAC;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA,SAASqL,yBAAyBA,CAAC2F,GAAG,EAAE;EACpC,IAAIA,GAAG,CAAC3Q,KAAK,IAAI2Q,GAAG,CAAC3G,MAAM,EAAE;IACzB,MAAM,IAAIrT,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,0DAA0D,GAC1J,kGAAkG,GAClG,oEAAoE,CAAC;EAC7E;AACJ;AACA;AACA;AACA;AACA;AACA,SAASmI,2BAA2BA,CAACyF,GAAG,EAAE3M,GAAG,EAAE2D,QAAQ,EAAE;EACrD,MAAMiH,QAAQ,GAAGA,CAAA,KAAM;IACnBE,oBAAoB,CAAC,CAAC;IACtBC,qBAAqB,CAAC,CAAC;IACvB,MAAMyD,cAAc,GAAGxO,GAAG,CAACmQ,YAAY;IACvC,IAAIxD,GAAG,CAACxG,IAAI,IAAIqI,cAAc,KAAK,CAAC,EAAE;MAClC7P,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,sCAAsC,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,8CAA8C,GACvJ,iFAAiF,GACjF,4EAA4E,GAC5E,8EAA8E,GAC9E,6DAA6D,CAAC,CAAC;IACvE;EACJ,CAAC;EACD,MAAM+L,oBAAoB,GAAGnH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,MAAM,EAAE4K,QAAQ,CAAC;EACnE;EACA,MAAMG,qBAAqB,GAAGpH,QAAQ,CAACsH,MAAM,CAACjL,GAAG,EAAE,OAAO,EAAE,MAAM;IAC9D8K,oBAAoB,CAAC,CAAC;IACtBC,qBAAqB,CAAC,CAAC;EAC3B,CAAC,CAAC;EACFG,yBAAyB,CAAClL,GAAG,EAAE4K,QAAQ,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA,SAAStD,uBAAuBA,CAACqF,GAAG,EAAE;EAClC,IAAIA,GAAG,CAAC1G,OAAO,IAAI0G,GAAG,CAAC1M,QAAQ,EAAE;IAC7B,MAAM,IAAItN,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,6BAA6B,GAC7H,mDAAmD,GACnD,wDAAwD,GACxD,sDAAsD,GACtD,sEAAsE,CAAC;EAC/E;EACA,MAAMqR,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC;EAC7C,IAAI,OAAOzD,GAAG,CAAC1G,OAAO,KAAK,QAAQ,IAAI,CAACmK,WAAW,CAACC,QAAQ,CAAC1D,GAAG,CAAC1G,OAAO,CAAC,EAAE;IACvE,MAAM,IAAItT,aAAa,CAAC,IAAI,CAAC,sCAAsC,GAAGmM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,6BAA6B,GAC7H,2BAA2B4N,GAAG,CAAC1G,OAAO,OAAO,GAC7C,kEAAkE,CAAC;EAC3E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwB,6BAA6BA,CAAC1I,KAAK,EAAEyG,WAAW,EAAE;EACvD,IAAIA,WAAW,KAAK3K,eAAe,EAAE;IACjC,IAAIyV,iBAAiB,GAAG,EAAE;IAC1B,KAAK,MAAMC,MAAM,IAAInL,gBAAgB,EAAE;MACnC,IAAImL,MAAM,CAACnU,OAAO,CAAC2C,KAAK,CAAC,EAAE;QACvBuR,iBAAiB,GAAGC,MAAM,CAACpU,IAAI;QAC/B;MACJ;IACJ;IACA,IAAImU,iBAAiB,EAAE;MACnB3R,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,+CAA+C,mEAAmE,GACpJ,GAAGyd,iBAAiB,4CAA4C,GAChE,8DAA8D,GAC9D,oCAAoCA,iBAAiB,aAAa,GAClE,iEAAiE,GACjE,gEAAgE,GAChE,6DAA6D,CAAC,CAAC;IACvE;EACJ;AACJ;AACA;AACA;AACA;AACA,SAAS5I,6BAA6BA,CAACiF,GAAG,EAAEnH,WAAW,EAAE;EACrD,IAAImH,GAAG,CAAC5G,QAAQ,IAAIP,WAAW,KAAK3K,eAAe,EAAE;IACjD8D,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,iDAAiD,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,6CAA6C,GACjK,sEAAsE,GACtE,4EAA4E,GAC5E,oFAAoF,CAAC,CAAC;EAC9F;AACJ;AACA;AACA;AACA;AACA;AACA,SAAS4I,iCAAiCA,CAACgF,GAAG,EAAEnH,WAAW,EAAE;EACzD,IAAImH,GAAG,CAACjQ,YAAY,IAAI8I,WAAW,KAAK3K,eAAe,EAAE;IACrD8D,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,iDAAiD,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,iDAAiD,GACrK,sEAAsE,GACtE,2FAA2F,GAC3F,+FAA+F,CAAC,CAAC;EACzG;AACJ;AACA;AACA;AACA;AAFA,SAGegJ,gCAAgCA,CAAAyI,EAAA;EAAA,OAAAC,iCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAc/C;AACA;AACA;AACA;AACA;AAJA,SAAAF,kCAAA;EAAAA,iCAAA,GAAAG,iBAAA,CAdA,WAAgDC,MAAM,EAAE;IACpD,IAAIvL,6BAA6B,KAAK,CAAC,EAAE;MACrCA,6BAA6B,EAAE;MAC/B,MAAMuL,MAAM,CAACC,UAAU,CAAC,CAAC;MACzB,IAAIxL,6BAA6B,GAAGD,wBAAwB,EAAE;QAC1D1G,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,qDAAqD,uEAAuEwS,wBAAwB,WAAWC,6BAA6B,WAAW,GACzO,oGAAoG,GACpG,mFAAmF,CAAC,CAAC;MAC7F;IACJ,CAAC,MACI;MACDA,6BAA6B,EAAE;IACnC;EACJ,CAAC;EAAA,OAAAmL,iCAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAMD,SAAS7H,2BAA2BA,CAAC6D,GAAG,EAAEjH,UAAU,EAAE;EAClD,MAAM0I,aAAa,GAAGxX,MAAM,CAACyX,gBAAgB,CAAC3I,UAAU,CAAC;EACzD,IAAI4I,aAAa,GAAG5E,UAAU,CAAC0E,aAAa,CAACG,gBAAgB,CAAC,OAAO,CAAC,CAAC;EACvE,IAAIC,cAAc,GAAG9E,UAAU,CAAC0E,aAAa,CAACG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;EACzE,IAAID,aAAa,GAAGrJ,2BAA2B,IAAIuJ,cAAc,GAAGvJ,2BAA2B,EAAE;IAC7FtG,OAAO,CAACC,IAAI,CAAC/L,mBAAmB,CAAC,IAAI,CAAC,6DAA6D,GAAGiM,mBAAmB,CAAC6N,GAAG,CAAC5N,KAAK,CAAC,iDAAiD,GACjL,sEAAsEkG,2BAA2B,MAAM,GACvG,oDAAoD,CAAC,CAAC;EAC9D;AACJ;AACA,SAASiG,yBAAyBA,CAAClL,GAAG,EAAE4K,QAAQ,EAAE;EAC9C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI5K,GAAG,CAAC+Q,QAAQ,IAAI/Q,GAAG,CAACiP,YAAY,EAAE;IAClCrE,QAAQ,CAAC,CAAC;EACd;AACJ;AACA,SAASgF,KAAKA,CAACzM,KAAK,EAAE;EAClB,OAAO6N,MAAM,CAACC,SAAS,CAAC9N,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,CAAC+N,OAAO,CAAC,CAAC,CAAC;AAC7D;AACA;AACA;AACA,SAASpF,aAAaA,CAACxN,KAAK,EAAE;EAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC3B,OAAOA,KAAK;EAChB;EACA,OAAOtK,gBAAgB,CAACsK,KAAK,CAAC;AAClC;AACA;AACA;AACA,SAASyN,qBAAqBA,CAACzN,KAAK,EAAE;EAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,EAAE,EAAE;IACpF,OAAOA,KAAK;EAChB;EACA,OAAO5K,gBAAgB,CAAC4K,KAAK,CAAC;AAClC;AAEA,SAASzJ,QAAQ,EAAEkG,YAAY,EAAEwK,gBAAgB,EAAEtD,0BAA0B,EAAE9L,OAAO,EAAEC,gBAAgB,EAAEhC,iBAAiB,EAAEwH,uBAAuB,EAAEW,uBAAuB,EAAEO,qBAAqB,EAAEQ,kBAAkB,EAAEQ,oBAAoB,EAAE/H,kBAAkB,EAAEW,oBAAoB,IAAIya,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}