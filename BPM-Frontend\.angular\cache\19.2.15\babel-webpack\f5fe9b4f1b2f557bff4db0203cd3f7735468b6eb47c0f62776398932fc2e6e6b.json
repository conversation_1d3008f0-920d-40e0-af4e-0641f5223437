{"ast": null, "code": "import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { WorkflowService } from './workflow.service';\nimport { environment } from '../../../environments/environment';\ndescribe('WorkflowService', () => {\n  let service;\n  let httpMock;\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [WorkflowService]\n    });\n    service = TestBed.inject(WorkflowService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n  afterEach(() => {\n    httpMock.verify();\n  });\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n  it('should fetch workflows and map API response correctly', () => {\n    const mockApiResponse = {\n      Data: [{\n        id: 'ddd57dd1-1742-4b3f-897b-ec3b24e01a2a',\n        name: 'new workflow',\n        description: 'just for test',\n        version: 1,\n        isActive: true,\n        steps: [],\n        createdAt: new Date('2025-08-07T00:26:28.298953'),\n        updatedAt: null\n      }],\n      TotalCount: 7,\n      PageNumber: 1,\n      PageSize: 10,\n      TotalPages: 1,\n      HasPreviousPage: false,\n      HasNextPage: false\n    };\n    const expectedResponse = {\n      data: mockApiResponse.Data,\n      totalCount: mockApiResponse.TotalCount,\n      pageNumber: mockApiResponse.PageNumber,\n      pageSize: mockApiResponse.PageSize,\n      totalPages: mockApiResponse.TotalPages,\n      hasPreviousPage: mockApiResponse.HasPreviousPage,\n      hasNextPage: mockApiResponse.HasNextPage\n    };\n    service.getWorkflows({\n      pageNumber: 1,\n      pageSize: 10,\n      sortBy: 'createdAt',\n      sortDirection: 'desc'\n    }).subscribe(response => {\n      expect(response).toEqual(expectedResponse);\n      expect(response.data.length).toBe(1);\n      expect(response.data[0].name).toBe('new workflow');\n      expect(response.totalCount).toBe(7);\n    });\n    const req = httpMock.expectOne(`${environment.apiUrl}/api/Workflow?pageNumber=1&pageSize=10&sortBy=createdAt&sortDirection=desc`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockApiResponse);\n  });\n  it('should handle empty workflow list', () => {\n    const mockApiResponse = {\n      Data: [],\n      TotalCount: 0,\n      PageNumber: 1,\n      PageSize: 10,\n      TotalPages: 0,\n      HasPreviousPage: false,\n      HasNextPage: false\n    };\n    service.getWorkflows().subscribe(response => {\n      expect(response.data.length).toBe(0);\n      expect(response.totalCount).toBe(0);\n    });\n    const req = httpMock.expectOne(`${environment.apiUrl}/api/Workflow`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockApiResponse);\n  });\n});", "map": {"version": 3, "names": ["TestBed", "HttpClientTestingModule", "HttpTestingController", "WorkflowService", "environment", "describe", "service", "httpMock", "beforeEach", "configureTestingModule", "imports", "providers", "inject", "after<PERSON>ach", "verify", "it", "expect", "toBeTruthy", "mockApiResponse", "Data", "id", "name", "description", "version", "isActive", "steps", "createdAt", "Date", "updatedAt", "TotalCount", "PageNumber", "PageSize", "TotalPages", "HasPreviousPage", "HasNextPage", "expectedResponse", "data", "totalCount", "pageNumber", "pageSize", "totalPages", "hasPreviousPage", "hasNextPage", "getWorkflows", "sortBy", "sortDirection", "subscribe", "response", "toEqual", "length", "toBe", "req", "expectOne", "apiUrl", "request", "method", "flush"], "sources": ["C:\\Users\\<USER>\\Desktop\\bpm project\\BPM-Frontend\\src\\app\\core\\services\\workflow.service.spec.ts"], "sourcesContent": ["import { TestBed } from '@angular/core/testing';\nimport { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';\nimport { WorkflowService } from './workflow.service';\nimport { WorkflowDto, PaginatedResponse, ApiPaginatedResponse } from '../models';\nimport { environment } from '../../../environments/environment';\n\ndescribe('WorkflowService', () => {\n  let service: WorkflowService;\n  let httpMock: HttpTestingController;\n\n  beforeEach(() => {\n    TestBed.configureTestingModule({\n      imports: [HttpClientTestingModule],\n      providers: [WorkflowService]\n    });\n    service = TestBed.inject(WorkflowService);\n    httpMock = TestBed.inject(HttpTestingController);\n  });\n\n  afterEach(() => {\n    httpMock.verify();\n  });\n\n  it('should be created', () => {\n    expect(service).toBeTruthy();\n  });\n\n  it('should fetch workflows and map API response correctly', () => {\n    const mockApiResponse: ApiPaginatedResponse<WorkflowDto> = {\n      Data: [\n        {\n          id: 'ddd57dd1-1742-4b3f-897b-ec3b24e01a2a',\n          name: 'new workflow',\n          description: 'just for test',\n          version: 1,\n          isActive: true,\n          steps: [],\n          createdAt: new Date('2025-08-07T00:26:28.298953'),\n          updatedAt: null\n        }\n      ],\n      TotalCount: 7,\n      PageNumber: 1,\n      PageSize: 10,\n      TotalPages: 1,\n      HasPreviousPage: false,\n      HasNextPage: false\n    };\n\n    const expectedResponse: PaginatedResponse<WorkflowDto> = {\n      data: mockApiResponse.Data,\n      totalCount: mockApiResponse.TotalCount,\n      pageNumber: mockApiResponse.PageNumber,\n      pageSize: mockApiResponse.PageSize,\n      totalPages: mockApiResponse.TotalPages,\n      hasPreviousPage: mockApiResponse.HasPreviousPage,\n      hasNextPage: mockApiResponse.HasNextPage\n    };\n\n    service.getWorkflows({\n      pageNumber: 1,\n      pageSize: 10,\n      sortBy: 'createdAt',\n      sortDirection: 'desc'\n    }).subscribe(response => {\n      expect(response).toEqual(expectedResponse);\n      expect(response.data.length).toBe(1);\n      expect(response.data[0].name).toBe('new workflow');\n      expect(response.totalCount).toBe(7);\n    });\n\n    const req = httpMock.expectOne(`${environment.apiUrl}/api/Workflow?pageNumber=1&pageSize=10&sortBy=createdAt&sortDirection=desc`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockApiResponse);\n  });\n\n  it('should handle empty workflow list', () => {\n    const mockApiResponse: ApiPaginatedResponse<WorkflowDto> = {\n      Data: [],\n      TotalCount: 0,\n      PageNumber: 1,\n      PageSize: 10,\n      TotalPages: 0,\n      HasPreviousPage: false,\n      HasNextPage: false\n    };\n\n    service.getWorkflows().subscribe(response => {\n      expect(response.data.length).toBe(0);\n      expect(response.totalCount).toBe(0);\n    });\n\n    const req = httpMock.expectOne(`${environment.apiUrl}/api/Workflow`);\n    expect(req.request.method).toBe('GET');\n    req.flush(mockApiResponse);\n  });\n});\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB;AAC/C,SAASC,uBAAuB,EAAEC,qBAAqB,QAAQ,8BAA8B;AAC7F,SAASC,eAAe,QAAQ,oBAAoB;AAEpD,SAASC,WAAW,QAAQ,mCAAmC;AAE/DC,QAAQ,CAAC,iBAAiB,EAAE,MAAK;EAC/B,IAAIC,OAAwB;EAC5B,IAAIC,QAA+B;EAEnCC,UAAU,CAAC,MAAK;IACdR,OAAO,CAACS,sBAAsB,CAAC;MAC7BC,OAAO,EAAE,CAACT,uBAAuB,CAAC;MAClCU,SAAS,EAAE,CAACR,eAAe;KAC5B,CAAC;IACFG,OAAO,GAAGN,OAAO,CAACY,MAAM,CAACT,eAAe,CAAC;IACzCI,QAAQ,GAAGP,OAAO,CAACY,MAAM,CAACV,qBAAqB,CAAC;EAClD,CAAC,CAAC;EAEFW,SAAS,CAAC,MAAK;IACbN,QAAQ,CAACO,MAAM,EAAE;EACnB,CAAC,CAAC;EAEFC,EAAE,CAAC,mBAAmB,EAAE,MAAK;IAC3BC,MAAM,CAACV,OAAO,CAAC,CAACW,UAAU,EAAE;EAC9B,CAAC,CAAC;EAEFF,EAAE,CAAC,uDAAuD,EAAE,MAAK;IAC/D,MAAMG,eAAe,GAAsC;MACzDC,IAAI,EAAE,CACJ;QACEC,EAAE,EAAE,sCAAsC;QAC1CC,IAAI,EAAE,cAAc;QACpBC,WAAW,EAAE,eAAe;QAC5BC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE,EAAE;QACTC,SAAS,EAAE,IAAIC,IAAI,CAAC,4BAA4B,CAAC;QACjDC,SAAS,EAAE;OACZ,CACF;MACDC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE;KACd;IAED,MAAMC,gBAAgB,GAAmC;MACvDC,IAAI,EAAElB,eAAe,CAACC,IAAI;MAC1BkB,UAAU,EAAEnB,eAAe,CAACW,UAAU;MACtCS,UAAU,EAAEpB,eAAe,CAACY,UAAU;MACtCS,QAAQ,EAAErB,eAAe,CAACa,QAAQ;MAClCS,UAAU,EAAEtB,eAAe,CAACc,UAAU;MACtCS,eAAe,EAAEvB,eAAe,CAACe,eAAe;MAChDS,WAAW,EAAExB,eAAe,CAACgB;KAC9B;IAED5B,OAAO,CAACqC,YAAY,CAAC;MACnBL,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,EAAE;MACZK,MAAM,EAAE,WAAW;MACnBC,aAAa,EAAE;KAChB,CAAC,CAACC,SAAS,CAACC,QAAQ,IAAG;MACtB/B,MAAM,CAAC+B,QAAQ,CAAC,CAACC,OAAO,CAACb,gBAAgB,CAAC;MAC1CnB,MAAM,CAAC+B,QAAQ,CAACX,IAAI,CAACa,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MACpClC,MAAM,CAAC+B,QAAQ,CAACX,IAAI,CAAC,CAAC,CAAC,CAACf,IAAI,CAAC,CAAC6B,IAAI,CAAC,cAAc,CAAC;MAClDlC,MAAM,CAAC+B,QAAQ,CAACV,UAAU,CAAC,CAACa,IAAI,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,MAAMC,GAAG,GAAG5C,QAAQ,CAAC6C,SAAS,CAAC,GAAGhD,WAAW,CAACiD,MAAM,4EAA4E,CAAC;IACjIrC,MAAM,CAACmC,GAAG,CAACG,OAAO,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;IACtCC,GAAG,CAACK,KAAK,CAACtC,eAAe,CAAC;EAC5B,CAAC,CAAC;EAEFH,EAAE,CAAC,mCAAmC,EAAE,MAAK;IAC3C,MAAMG,eAAe,GAAsC;MACzDC,IAAI,EAAE,EAAE;MACRU,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,CAAC;MACbC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE;KACd;IAED5B,OAAO,CAACqC,YAAY,EAAE,CAACG,SAAS,CAACC,QAAQ,IAAG;MAC1C/B,MAAM,CAAC+B,QAAQ,CAACX,IAAI,CAACa,MAAM,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;MACpClC,MAAM,CAAC+B,QAAQ,CAACV,UAAU,CAAC,CAACa,IAAI,CAAC,CAAC,CAAC;IACrC,CAAC,CAAC;IAEF,MAAMC,GAAG,GAAG5C,QAAQ,CAAC6C,SAAS,CAAC,GAAGhD,WAAW,CAACiD,MAAM,eAAe,CAAC;IACpErC,MAAM,CAACmC,GAAG,CAACG,OAAO,CAACC,MAAM,CAAC,CAACL,IAAI,CAAC,KAAK,CAAC;IACtCC,GAAG,CAACK,KAAK,CAACtC,eAAe,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}