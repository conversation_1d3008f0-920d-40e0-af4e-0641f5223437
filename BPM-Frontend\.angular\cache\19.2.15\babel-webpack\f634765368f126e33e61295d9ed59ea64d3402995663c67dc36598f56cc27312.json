{"ast": null, "code": "export function createInvalidObservableTypeError(input) {\n  return new TypeError(`You provided ${input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`);\n}", "map": {"version": 3, "names": ["createInvalidObservableTypeError", "input", "TypeError"], "sources": ["C:/Users/<USER>/Desktop/bpm project/BPM-Frontend/node_modules/rxjs/dist/esm/internal/util/throwUnobservableError.js"], "sourcesContent": ["export function createInvalidObservableTypeError(input) {\n    return new TypeError(`You provided ${input !== null && typeof input === 'object' ? 'an invalid object' : `'${input}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`);\n}\n"], "mappings": "AAAA,OAAO,SAASA,gCAAgCA,CAACC,KAAK,EAAE;EACpD,OAAO,IAAIC,SAAS,CAAC,gBAAgBD,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAG,mBAAmB,GAAG,IAAIA,KAAK,GAAG,0HAA0H,CAAC;AACpP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}