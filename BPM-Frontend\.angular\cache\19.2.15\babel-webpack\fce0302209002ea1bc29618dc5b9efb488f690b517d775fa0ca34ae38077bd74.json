{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { map } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\nlet WorkflowService = class WorkflowService {\n  http;\n  API_URL = `${environment.apiUrl}/api/Workflow`;\n  constructor(http) {\n    this.http = http;\n  }\n  getWorkflows(params) {\n    let httpParams = new HttpParams();\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n    return this.http.get(this.API_URL, {\n      params: httpParams\n    }).pipe(map(apiResponse => ({\n      data: apiResponse.Data,\n      totalCount: apiResponse.TotalCount,\n      pageNumber: apiResponse.PageNumber,\n      pageSize: apiResponse.PageSize,\n      totalPages: apiResponse.TotalPages,\n      hasPreviousPage: apiResponse.HasPreviousPage,\n      hasNextPage: apiResponse.HasNextPage\n    })));\n  }\n  getActiveWorkflows() {\n    return this.http.get(`${this.API_URL}/active`);\n  }\n  getWorkflowById(id) {\n    return this.http.get(`${this.API_URL}/${id}`);\n  }\n  getWorkflowWithSteps(id) {\n    return this.http.get(`${this.API_URL}/${id}/steps`);\n  }\n  createWorkflow(workflow) {\n    return this.http.post(this.API_URL, workflow);\n  }\n  updateWorkflow(id, workflow) {\n    return this.http.put(`${this.API_URL}/${id}`, workflow);\n  }\n  deleteWorkflow(id) {\n    return this.http.delete(`${this.API_URL}/${id}`);\n  }\n  // Get workflow steps\n  getWorkflowSteps(workflowId) {\n    return this.http.get(`${this.API_URL}/${workflowId}/steps`);\n  }\n  activateWorkflow(id) {\n    return this.http.patch(`${this.API_URL}/${id}/activate`, {});\n  }\n  deactivateWorkflow(id) {\n    return this.http.patch(`${this.API_URL}/${id}/deactivate`, {});\n  }\n  duplicateWorkflow(id) {\n    return this.http.post(`${this.API_URL}/${id}/duplicate`, {});\n  }\n  getWorkflowVersions(workflowName) {\n    return this.http.get(`${this.API_URL}/versions/${workflowName}`);\n  }\n  static ctorParameters = () => [{\n    type: HttpClient\n  }];\n};\nWorkflowService = __decorate([Injectable({\n  providedIn: 'root'\n})], WorkflowService);\nexport { WorkflowService };", "map": {"version": 3, "names": ["Injectable", "HttpClient", "HttpParams", "map", "environment", "WorkflowService", "http", "API_URL", "apiUrl", "constructor", "getWorkflows", "params", "httpParams", "pageNumber", "set", "toString", "pageSize", "searchTerm", "sortBy", "sortDirection", "get", "pipe", "apiResponse", "data", "Data", "totalCount", "TotalCount", "PageNumber", "PageSize", "totalPages", "TotalPages", "hasPreviousPage", "HasPreviousPage", "hasNextPage", "HasNextPage", "getActiveWorkflows", "getWorkflowById", "id", "getWorkflowWithSteps", "createWorkflow", "workflow", "post", "updateWorkflow", "put", "deleteWorkflow", "delete", "getWorkflowSteps", "workflowId", "activateWorkflow", "patch", "deactivateWorkflow", "duplicateWorkflow", "getWorkflowVersions", "workflowName", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\bpm project\\BPM-Frontend\\src\\app\\core\\services\\workflow.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams } from '@angular/common/http';\nimport { Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport {\n  WorkflowDto,\n  CreateWorkflowDto,\n  UpdateWorkflowDto,\n  PaginatedResponse,\n  ApiPaginatedResponse,\n  PaginationParams\n} from '../models';\nimport { environment } from '../../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class WorkflowService {\n  private readonly API_URL = `${environment.apiUrl}/api/Workflow`;\n\n  constructor(private readonly http: HttpClient) {}\n\n  getWorkflows(params?: PaginationParams): Observable<PaginatedResponse<WorkflowDto>> {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.pageNumber) httpParams = httpParams.set('pageNumber', params.pageNumber.toString());\n      if (params.pageSize) httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      if (params.searchTerm) httpParams = httpParams.set('searchTerm', params.searchTerm);\n      if (params.sortBy) httpParams = httpParams.set('sortBy', params.sortBy);\n      if (params.sortDirection) httpParams = httpParams.set('sortDirection', params.sortDirection);\n    }\n\n    return this.http.get<ApiPaginatedResponse<WorkflowDto>>(this.API_URL, { params: httpParams }).pipe(\n      map(apiResponse => ({\n        data: apiResponse.Data,\n        totalCount: apiResponse.TotalCount,\n        pageNumber: apiResponse.PageNumber,\n        pageSize: apiResponse.PageSize,\n        totalPages: apiResponse.TotalPages,\n        hasPreviousPage: apiResponse.HasPreviousPage,\n        hasNextPage: apiResponse.HasNextPage\n      }))\n    );\n  }\n\n  getActiveWorkflows(): Observable<WorkflowDto[]> {\n    return this.http.get<WorkflowDto[]>(`${this.API_URL}/active`);\n  }\n\n  getWorkflowById(id: string): Observable<WorkflowDto> {\n    return this.http.get<WorkflowDto>(`${this.API_URL}/${id}`);\n  }\n\n  getWorkflowWithSteps(id: string): Observable<WorkflowDto> {\n    return this.http.get<WorkflowDto>(`${this.API_URL}/${id}/steps`);\n  }\n\n  createWorkflow(workflow: CreateWorkflowDto): Observable<WorkflowDto> {\n    return this.http.post<WorkflowDto>(this.API_URL, workflow);\n  }\n\n  updateWorkflow(id: string, workflow: UpdateWorkflowDto): Observable<WorkflowDto> {\n    return this.http.put<WorkflowDto>(`${this.API_URL}/${id}`, workflow);\n  }\n\n  deleteWorkflow(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.API_URL}/${id}`);\n  }\n\n  // Get workflow steps\n  getWorkflowSteps(workflowId: string): Observable<any[]> {\n    return this.http.get<any[]>(`${this.API_URL}/${workflowId}/steps`);\n  }\n\n  activateWorkflow(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/activate`, {});\n  }\n\n  deactivateWorkflow(id: string): Observable<void> {\n    return this.http.patch<void>(`${this.API_URL}/${id}/deactivate`, {});\n  }\n\n  duplicateWorkflow(id: string): Observable<WorkflowDto> {\n    return this.http.post<WorkflowDto>(`${this.API_URL}/${id}/duplicate`, {});\n  }\n\n  getWorkflowVersions(workflowName: string): Observable<WorkflowDto[]> {\n    return this.http.get<WorkflowDto[]>(`${this.API_URL}/versions/${workflowName}`);\n  }\n}\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,EAAEC,UAAU,QAAQ,sBAAsB;AAE7D,SAASC,GAAG,QAAQ,gBAAgB;AASpC,SAASC,WAAW,QAAQ,mCAAmC;AAKxD,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAGGC,IAAA;EAFZC,OAAO,GAAG,GAAGH,WAAW,CAACI,MAAM,eAAe;EAE/DC,YAA6BH,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAe;EAEhDI,YAAYA,CAACC,MAAyB;IACpC,IAAIC,UAAU,GAAG,IAAIV,UAAU,EAAE;IAEjC,IAAIS,MAAM,EAAE;MACV,IAAIA,MAAM,CAACE,UAAU,EAAED,UAAU,GAAGA,UAAU,CAACE,GAAG,CAAC,YAAY,EAAEH,MAAM,CAACE,UAAU,CAACE,QAAQ,EAAE,CAAC;MAC9F,IAAIJ,MAAM,CAACK,QAAQ,EAAEJ,UAAU,GAAGA,UAAU,CAACE,GAAG,CAAC,UAAU,EAAEH,MAAM,CAACK,QAAQ,CAACD,QAAQ,EAAE,CAAC;MACxF,IAAIJ,MAAM,CAACM,UAAU,EAAEL,UAAU,GAAGA,UAAU,CAACE,GAAG,CAAC,YAAY,EAAEH,MAAM,CAACM,UAAU,CAAC;MACnF,IAAIN,MAAM,CAACO,MAAM,EAAEN,UAAU,GAAGA,UAAU,CAACE,GAAG,CAAC,QAAQ,EAAEH,MAAM,CAACO,MAAM,CAAC;MACvE,IAAIP,MAAM,CAACQ,aAAa,EAAEP,UAAU,GAAGA,UAAU,CAACE,GAAG,CAAC,eAAe,EAAEH,MAAM,CAACQ,aAAa,CAAC;IAC9F;IAEA,OAAO,IAAI,CAACb,IAAI,CAACc,GAAG,CAAoC,IAAI,CAACb,OAAO,EAAE;MAAEI,MAAM,EAAEC;IAAU,CAAE,CAAC,CAACS,IAAI,CAChGlB,GAAG,CAACmB,WAAW,KAAK;MAClBC,IAAI,EAAED,WAAW,CAACE,IAAI;MACtBC,UAAU,EAAEH,WAAW,CAACI,UAAU;MAClCb,UAAU,EAAES,WAAW,CAACK,UAAU;MAClCX,QAAQ,EAAEM,WAAW,CAACM,QAAQ;MAC9BC,UAAU,EAAEP,WAAW,CAACQ,UAAU;MAClCC,eAAe,EAAET,WAAW,CAACU,eAAe;MAC5CC,WAAW,EAAEX,WAAW,CAACY;KAC1B,CAAC,CAAC,CACJ;EACH;EAEAC,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC7B,IAAI,CAACc,GAAG,CAAgB,GAAG,IAAI,CAACb,OAAO,SAAS,CAAC;EAC/D;EAEA6B,eAAeA,CAACC,EAAU;IACxB,OAAO,IAAI,CAAC/B,IAAI,CAACc,GAAG,CAAc,GAAG,IAAI,CAACb,OAAO,IAAI8B,EAAE,EAAE,CAAC;EAC5D;EAEAC,oBAAoBA,CAACD,EAAU;IAC7B,OAAO,IAAI,CAAC/B,IAAI,CAACc,GAAG,CAAc,GAAG,IAAI,CAACb,OAAO,IAAI8B,EAAE,QAAQ,CAAC;EAClE;EAEAE,cAAcA,CAACC,QAA2B;IACxC,OAAO,IAAI,CAAClC,IAAI,CAACmC,IAAI,CAAc,IAAI,CAAClC,OAAO,EAAEiC,QAAQ,CAAC;EAC5D;EAEAE,cAAcA,CAACL,EAAU,EAAEG,QAA2B;IACpD,OAAO,IAAI,CAAClC,IAAI,CAACqC,GAAG,CAAc,GAAG,IAAI,CAACpC,OAAO,IAAI8B,EAAE,EAAE,EAAEG,QAAQ,CAAC;EACtE;EAEAI,cAAcA,CAACP,EAAU;IACvB,OAAO,IAAI,CAAC/B,IAAI,CAACuC,MAAM,CAAO,GAAG,IAAI,CAACtC,OAAO,IAAI8B,EAAE,EAAE,CAAC;EACxD;EAEA;EACAS,gBAAgBA,CAACC,UAAkB;IACjC,OAAO,IAAI,CAACzC,IAAI,CAACc,GAAG,CAAQ,GAAG,IAAI,CAACb,OAAO,IAAIwC,UAAU,QAAQ,CAAC;EACpE;EAEAC,gBAAgBA,CAACX,EAAU;IACzB,OAAO,IAAI,CAAC/B,IAAI,CAAC2C,KAAK,CAAO,GAAG,IAAI,CAAC1C,OAAO,IAAI8B,EAAE,WAAW,EAAE,EAAE,CAAC;EACpE;EAEAa,kBAAkBA,CAACb,EAAU;IAC3B,OAAO,IAAI,CAAC/B,IAAI,CAAC2C,KAAK,CAAO,GAAG,IAAI,CAAC1C,OAAO,IAAI8B,EAAE,aAAa,EAAE,EAAE,CAAC;EACtE;EAEAc,iBAAiBA,CAACd,EAAU;IAC1B,OAAO,IAAI,CAAC/B,IAAI,CAACmC,IAAI,CAAc,GAAG,IAAI,CAAClC,OAAO,IAAI8B,EAAE,YAAY,EAAE,EAAE,CAAC;EAC3E;EAEAe,mBAAmBA,CAACC,YAAoB;IACtC,OAAO,IAAI,CAAC/C,IAAI,CAACc,GAAG,CAAgB,GAAG,IAAI,CAACb,OAAO,aAAa8C,YAAY,EAAE,CAAC;EACjF;;;;;AAxEWhD,eAAe,GAAAiD,UAAA,EAH3BtD,UAAU,CAAC;EACVuD,UAAU,EAAE;CACb,CAAC,C,EACWlD,eAAe,CAyE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}