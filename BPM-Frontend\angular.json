{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"BPM-Frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/bpm-frontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}, {"glob": "**/*", "input": "node_modules/@mdi/font/fonts", "output": "/assets/fonts/"}], "styles": ["src/styles.scss", "node_modules/@mdi/font/css/materialdesignicons.min.css"], "scripts": [], "outputMode": "static"}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "8kB", "maximumError": "16kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "budgets": []}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "BPM-Frontend:build:production"}, "development": {"buildTarget": "BPM-Frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}