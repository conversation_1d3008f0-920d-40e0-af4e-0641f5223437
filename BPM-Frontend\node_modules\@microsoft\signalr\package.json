{"name": "@microsoft/signalr", "version": "9.0.6", "description": "ASP.NET Core SignalR Client", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "typings": "./dist/esm/index.d.ts", "umd": "./dist/browser/signalr.js", "umd_name": "signalR", "unpkg": "./dist/browser/signalr.js", "directories": {"test": "spec"}, "sideEffects": false, "scripts": {"clean": "rimraf ./dist", "prebuild": "rimraf ./src/pkg-version.ts && node -e \"const fs = require('fs'); const packageJson = require('./package.json'); fs.writeFileSync('./src/pkg-version.ts', 'export const VERSION = \\'' + packageJson.version + '\\';');\"", "build": "npm run build:esm && npm run build:cjs && npm run build:browser && npm run build:webworker", "build:esm": "tsc --project ./tsconfig.json --module es2015 --outDir ./dist/esm -d", "build:cjs": "tsc --project ./tsconfig.json --module commonjs --outDir ./dist/cjs", "build:browser": "webpack-cli", "build:webworker": "webpack-cli --env platform=webworker", "get-version": "node -e \"const { name, version } = require('./package.json'); console.log(`${name};${version}`);\""}, "keywords": ["signalr", "aspnetcore"], "repository": {"type": "git", "url": "git+https://github.com/dotnet/aspnetcore.git"}, "author": "Microsoft", "license": "MIT", "bugs": {"url": "https://github.com/dotnet/aspnetcore/issues"}, "homepage": "https://github.com/dotnet/aspnetcore/tree/main/src/SignalR#readme", "files": ["dist/**/*", "src/**/*"], "dependencies": {"abort-controller": "^3.0.0", "eventsource": "^2.0.2", "fetch-cookie": "^2.0.3", "node-fetch": "^2.6.7", "ws": "^7.5.10"}, "overrides": {"ansi-regex": "5.0.1", "tough-cookie": ">=4.1.3"}}