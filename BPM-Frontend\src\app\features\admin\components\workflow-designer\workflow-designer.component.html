<div class="workflow-designer-container">
  <mat-card>
    <mat-card-header>
      <mat-card-title>
        <mat-icon>account_tree</mat-icon>
        Workflow Designer
      </mat-card-title>
      <div class="header-actions">
        <button mat-raised-button color="primary">
          <mat-icon>add</mat-icon>
          New Workflow
        </button>
      </div>
    </mat-card-header>

    <mat-card-content>
      <div class="designer-placeholder">
        <div class="placeholder-icon">
          <mat-icon>account_tree</mat-icon>
        </div>
        <h2>Workflow Designer</h2>
        <p>Design and manage business process workflows with our visual workflow designer.</p>

        <div class="feature-list">
          <div class="feature-item">
            <mat-icon>drag_indicator</mat-icon>
            <span>Drag & Drop Interface</span>
          </div>
          <div class="feature-item">
            <mat-icon>settings</mat-icon>
            <span>Configurable Steps</span>
          </div>
          <div class="feature-item">
            <mat-icon>people</mat-icon>
            <span>Role-based Assignments</span>
          </div>
          <div class="feature-item">
            <mat-icon>timeline</mat-icon>
            <span>Process Automation</span>
          </div>
        </div>

        <div class="action-buttons">
          <button mat-raised-button color="primary">
            <mat-icon>add</mat-icon>
            Create New Workflow
          </button>
          <button mat-button>
            <mat-icon>folder_open</mat-icon>
            Browse Templates
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
