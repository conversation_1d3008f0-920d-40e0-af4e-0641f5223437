.register-card {
  width: 100%;
  border: none;
  box-shadow: none;
  background: transparent;
  
  mat-card-header {
    text-align: center;
    margin-bottom: 1.5rem;
    
    mat-card-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 0.5rem;
    }
    
    mat-card-subtitle {
      color: #666;
      font-size: 0.9rem;
    }
  }
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  
  .full-width {
    width: 100%;
  }
  
  .half-width {
    width: calc(50% - 0.5rem);
  }
  
  .name-row {
    display: flex;
    gap: 1rem;
  }
  
  mat-form-field {
    .mat-mdc-form-field-subscript-wrapper {
      margin-top: 0.5rem;
    }
  }
}

.terms-section {
  margin: 0.5rem 0 1.5rem 0;
  
  mat-checkbox {
    font-size: 0.875rem;
    
    .mat-checkbox-label {
      line-height: 1.4;
    }
  }
  
  .terms-link {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: #764ba2;
      text-decoration: underline;
    }
  }
  
  mat-error {
    font-size: 0.75rem;
    color: #f44336;
    margin-top: 0.5rem;
    display: block;
  }
}

.register-button {
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: all 0.3s ease;
  
  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
  
  mat-spinner {
    margin-right: 8px;
  }
}

.card-actions {
  padding: 1rem 0 0 0;
  justify-content: center;
  
  .signin-text {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
    margin: 0;
    
    .signin-link {
      color: #667eea;
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
      
      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }
  }
}

// Custom snackbar styles
:host ::ng-deep {
  .success-snackbar {
    background-color: #4caf50;
    color: white;
  }
  
  .error-snackbar {
    background-color: #f44336;
    color: white;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .name-row {
    flex-direction: column;
    gap: 1rem;
    
    .half-width {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .register-button {
    height: 44px;
    font-size: 0.9rem;
  }
  
  .terms-section {
    mat-checkbox {
      font-size: 0.8rem;
    }
  }
}

// Animation for form elements
.register-form {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Password strength indicator (optional enhancement)
.password-strength {
  margin-top: 0.5rem;
  
  .strength-bar {
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    
    .strength-fill {
      height: 100%;
      transition: all 0.3s ease;
      
      &.weak {
        width: 33%;
        background-color: #f44336;
      }
      
      &.medium {
        width: 66%;
        background-color: #ff9800;
      }
      
      &.strong {
        width: 100%;
        background-color: #4caf50;
      }
    }
  }
  
  .strength-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    
    &.weak { color: #f44336; }
    &.medium { color: #ff9800; }
    &.strong { color: #4caf50; }
  }
}