import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Subject, takeUntil } from 'rxjs';

import { AuthService } from '../../../../core/services/auth.service';
import { ReportingService } from '../../../../core/services/reporting.service';

@Component({
  selector: 'app-reporting-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatTableModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './reporting-dashboard.component.html',
  styleUrls: ['./reporting-dashboard.component.scss']
})
export class ReportingDashboardComponent implements OnInit, OnDestroy {
  private readonly destroy$ = new Subject<void>();

  isLoading = false;
  currentUser: any = null;

  // Dashboard stats
  systemStats = {
    totalUsers: 0,
    activeWorkflows: 0,
    totalRequests: 0,
    pendingApprovals: 0
  };

  constructor(
    private readonly router: Router,
    private readonly authService: AuthService,
    private readonly reportingService: ReportingService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
    this.loadSystemStats();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private loadUserData(): void {
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUser = user;
      });
  }

  private loadSystemStats(): void {
    this.isLoading = true;

    // Mock data for now - replace with actual API calls
    setTimeout(() => {
      this.systemStats = {
        totalUsers: 156,
        activeWorkflows: 12,
        totalRequests: 1247,
        pendingApprovals: 23
      };
      this.isLoading = false;
    }, 1000);
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
}
