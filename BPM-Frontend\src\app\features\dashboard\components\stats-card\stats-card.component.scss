.stats-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;

    i {
      font-size: 24px;
      color: white;
    }
  }

  .stats-content {
    flex: 1;

    .stats-value {
      font-size: 2rem;
      font-weight: 700;
      color: #333;
      line-height: 1;
    }

    .stats-title {
      font-size: 0.9rem;
      color: #666;
      margin-top: 5px;
    }
  }

  &.stats-card-primary .stats-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.stats-card-success .stats-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.stats-card-warning .stats-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  &.stats-card-info .stats-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  &.stats-card-danger .stats-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
}

@media (max-width: 768px) {
  .stats-card {
    padding: 15px;

    .stats-icon {
      width: 50px;
      height: 50px;
      margin-right: 12px;

      i {
        font-size: 20px;
      }
    }

    .stats-content .stats-value {
      font-size: 1.5rem;
    }
  }
}
