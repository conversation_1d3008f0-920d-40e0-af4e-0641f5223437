import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Subject, takeUntil } from 'rxjs';

import { RequestService } from '../../../../core/services/request.service';
import { AuthService } from '../../../../core/services/auth.service';
import { RequestDto, RequestType, RequestStatus, StepStatus } from '../../../../core/models';

@Component({
  selector: 'app-request-details',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    MatStepperModule,
    MatTooltipModule
  ],
  template: `
    <div class="request-details-container">
      <!-- Loading Spinner -->
      <div *ngIf="loading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <!-- Request Details -->
      <div *ngIf="!loading && request">
        <!-- Header Card -->
        <mat-card class="header-card">
          <mat-card-header>
            <mat-card-title>
              <div class="title-section">
                <mat-icon>assignment</mat-icon>
                <div>
                  <h2>{{request.title || 'Request Details'}}</h2>
                  <span class="request-type">{{getRequestTypeLabel(request.type)}}</span>
                </div>
              </div>
              <div class="status-section">
                <mat-chip [class]="getStatusClass(request.status)">
                  {{getStatusLabel(request.status)}}
                </mat-chip>
              </div>
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <div class="request-info">
              <div class="info-item">
                <strong>Request ID:</strong>
                <span class="request-id">{{request.id}}</span>
              </div>
              <div class="info-item">
                <strong>Submitted by:</strong>
                <span>{{request.initiatorName}}</span>
              </div>
              <div class="info-item">
                <strong>Created:</strong>
                <span>{{request.createdAt | date:'full'}}</span>
              </div>
              <div class="info-item" *ngIf="request.updatedAt">
                <strong>Last Updated:</strong>
                <span>{{request.updatedAt | date:'full'}}</span>
              </div>
            </div>

            <mat-divider></mat-divider>

            <div class="description-section" *ngIf="request.description">
              <h3>Description</h3>
              <p>{{request.description}}</p>
            </div>
          </mat-card-content>

          <mat-card-actions>
            <button mat-button routerLink="/requests">
              <mat-icon>arrow_back</mat-icon>
              Back to Requests
            </button>
            <button mat-raised-button color="primary" *ngIf="canEditRequest()" [routerLink]="['/requests/edit', request.id]">
              <mat-icon>edit</mat-icon>
              Edit Request
            </button>
          </mat-card-actions>
        </mat-card>

        <!-- Workflow Steps -->
        <mat-card class="workflow-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>timeline</mat-icon>
              Workflow Progress
            </mat-card-title>
          </mat-card-header>

          <mat-card-content>
            <mat-stepper [linear]="false" orientation="vertical" class="workflow-stepper">
              <mat-step *ngFor="let step of request.requestSteps; let i = index" 
                        [completed]="step.status === StepStatus.Approved"
                        [hasError]="step.status === StepStatus.Rejected">
                <ng-template matStepLabel>
                  <div class="step-label">
                    <span class="step-name">{{step.workflowStepName}}</span>
                    <span class="step-role">({{step.responsibleRole}})</span>
                  </div>
                </ng-template>

                <div class="step-content">
                  <div class="step-status">
                    <mat-chip [class]="getStepStatusClass(step.status)">
                      {{getStepStatusLabel(step.status)}}
                    </mat-chip>
                  </div>

                  <div class="step-details" *ngIf="step.validatedAt || step.validatorName">
                    <div *ngIf="step.validatorName" class="step-detail">
                      <strong>Processed by:</strong> {{step.validatorName}}
                    </div>
                    <div *ngIf="step.validatedAt" class="step-detail">
                      <strong>Date:</strong> {{step.validatedAt | date:'short'}}
                    </div>
                    <div *ngIf="step.comments" class="step-detail">
                      <strong>Comments:</strong>
                      <p class="step-comments">{{step.comments}}</p>
                    </div>
                  </div>

                  <div *ngIf="step.status === StepStatus.Pending" class="pending-message">
                    <mat-icon>schedule</mat-icon>
                    <span>Waiting for approval from {{step.responsibleRole}}</span>
                  </div>
                </div>
              </mat-step>
            </mat-stepper>
          </mat-card-content>
        </mat-card>
      </div>

      <!-- Error State -->
      <div *ngIf="!loading && !request" class="error-container">
        <mat-card>
          <mat-card-content>
            <div class="error-content">
              <mat-icon>error</mat-icon>
              <h3>Request Not Found</h3>
              <p>The request you're looking for doesn't exist or you don't have permission to view it.</p>
              <button mat-raised-button color="primary" routerLink="/requests">
                Back to Requests
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .request-details-container {
      padding: 1rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      padding: 3rem;
    }

    .header-card {
      margin-bottom: 1rem;
    }

    .title-section {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .title-section h2 {
      margin: 0;
      font-size: 1.5rem;
    }

    .request-type {
      color: #666;
      font-size: 0.9rem;
      font-weight: normal;
    }

    mat-card-title {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      width: 100%;
    }

    .request-info {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
      margin: 1rem 0;
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .request-id {
      font-family: monospace;
      background-color: #f5f5f5;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.9rem;
    }

    .description-section {
      margin-top: 1rem;
    }

    .description-section h3 {
      margin-bottom: 0.5rem;
      color: #333;
    }

    .description-section p {
      line-height: 1.6;
      color: #666;
    }

    .workflow-card {
      margin-top: 1rem;
    }

    .workflow-stepper {
      margin-top: 1rem;
    }

    .step-label {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }

    .step-name {
      font-weight: 500;
    }

    .step-role {
      font-size: 0.8rem;
      color: #666;
    }

    .step-content {
      padding: 1rem 0;
    }

    .step-status {
      margin-bottom: 1rem;
    }

    .step-details {
      background-color: #f8f9fa;
      padding: 1rem;
      border-radius: 4px;
      margin-top: 1rem;
    }

    .step-detail {
      margin-bottom: 0.5rem;
    }

    .step-comments {
      margin: 0.5rem 0 0 0;
      padding: 0.5rem;
      background-color: white;
      border-left: 3px solid #2196f3;
      border-radius: 0 4px 4px 0;
    }

    .pending-message {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #ff9800;
      font-style: italic;
    }

    .status-pending { background-color: #fff3cd; color: #856404; }
    .status-approved { background-color: #d4edda; color: #155724; }
    .status-rejected { background-color: #f8d7da; color: #721c24; }
    .status-archived { background-color: #e2e3e5; color: #383d41; }

    .step-status-pending { background-color: #fff3cd; color: #856404; }
    .step-status-approved { background-color: #d4edda; color: #155724; }
    .step-status-rejected { background-color: #f8d7da; color: #721c24; }

    .error-container {
      display: flex;
      justify-content: center;
      padding: 2rem;
    }

    .error-content {
      text-align: center;
      padding: 2rem;
    }

    .error-content mat-icon {
      font-size: 4rem;
      width: 4rem;
      height: 4rem;
      color: #f44336;
      margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
      mat-card-title {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .request-info {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class RequestDetailsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  request: RequestDto | null = null;
  loading = false;
  requestId: string;

  // Enums for template
  RequestStatus = RequestStatus;
  RequestType = RequestType;
  StepStatus = StepStatus;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private requestService: RequestService,
    private authService: AuthService
  ) {
    this.requestId = this.route.snapshot.params['id'];
  }

  ngOnInit(): void {
    this.loadRequestDetails();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadRequestDetails(): void {
    this.loading = true;
    
    this.requestService.getRequestById(this.requestId).pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (request) => {
        this.request = request;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading request details:', error);
        this.request = null;
        this.loading = false;
      }
    });
  }

  getRequestTypeLabel(type: RequestType): string {
    switch (type) {
      case RequestType.Leave: return 'Leave Request';
      case RequestType.Expense: return 'Expense Report';
      case RequestType.Training: return 'Training Request';
      case RequestType.ITSupport: return 'IT Support';
      case RequestType.ProfileUpdate: return 'Profile Update';
      default: return 'Unknown';
    }
  }

  getStatusLabel(status: RequestStatus): string {
    switch (status) {
      case RequestStatus.Pending: return 'Pending';
      case RequestStatus.Approved: return 'Approved';
      case RequestStatus.Rejected: return 'Rejected';
      case RequestStatus.Archived: return 'Archived';
      default: return 'Unknown';
    }
  }

  getStatusClass(status: RequestStatus): string {
    switch (status) {
      case RequestStatus.Pending: return 'status-pending';
      case RequestStatus.Approved: return 'status-approved';
      case RequestStatus.Rejected: return 'status-rejected';
      case RequestStatus.Archived: return 'status-archived';
      default: return '';
    }
  }

  getStepStatusLabel(status: StepStatus): string {
    switch (status) {
      case StepStatus.Pending: return 'Pending';
      case StepStatus.Approved: return 'Approved';
      case StepStatus.Rejected: return 'Rejected';
      default: return 'Unknown';
    }
  }

  getStepStatusClass(status: StepStatus): string {
    switch (status) {
      case StepStatus.Pending: return 'step-status-pending';
      case StepStatus.Approved: return 'step-status-approved';
      case StepStatus.Rejected: return 'step-status-rejected';
      default: return '';
    }
  }

  canEditRequest(): boolean {
    if (!this.request) return false;
    
    const currentUser = this.authService.getCurrentUser();
    return this.request.status === RequestStatus.Pending && 
           this.request.initiatorId === currentUser?.id;
  }
}
