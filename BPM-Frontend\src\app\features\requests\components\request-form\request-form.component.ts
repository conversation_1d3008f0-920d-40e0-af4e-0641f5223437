import { CommonModule } from '@angular/common';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Mat<PERSON>nackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Subject, takeUntil, forkJoin } from 'rxjs';

import { RequestService } from '../../../../core/services/request.service';
import { WorkflowService } from '../../../../core/services/workflow.service';
import { CreateRequestDto, RequestType, WorkflowDto } from '../../../../core/models';

@Component({
  selector: 'app-request-form',
  templateUrl: './request-form.component.html',
  styleUrls: ['./request-form.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, MatSnackBarModule]
})
export class RequestFormComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  requestForm!: FormGroup;
  requestType: string = '';
  isSubmitting = false;
  availableWorkflows: WorkflowDto[] = [];
  loading = false;

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private requestService: RequestService,
    private workflowService: WorkflowService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.route.paramMap.subscribe((params) => {
      this.requestType = params.get('type') || '';
      this.loadWorkflows();
    });
  }

  loadWorkflows(): void {
    this.loading = true;
    this.workflowService.getActiveWorkflows().pipe(
      takeUntil(this.destroy$)
    ).subscribe({
      next: (workflows) => {
        this.availableWorkflows = workflows;
        this.initForm();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading workflows:', error);
        this.snackBar.open('Error loading workflows. Using default workflow.', 'Close', { duration: 3000 });
        this.initForm();
        this.loading = false;
      }
    });
  }

  initForm(): void {
    // This is a placeholder. In a real application, you would fetch form configurations
    // based on the requestType from a service or backend.
    switch (this.requestType) {
      case 'leave':
        this.requestForm = this.fb.group({
          startDate: ['', Validators.required],
          endDate: ['', Validators.required],
          reason: ['', Validators.required],
        });
        break;
      case 'expense':
        this.requestForm = this.fb.group({
          amount: ['', [Validators.required, Validators.min(0)]],
          description: ['', Validators.required],
          receipt: [null],
        });
        break;
      case 'training':
        this.requestForm = this.fb.group({
          courseName: ['', Validators.required],
          provider: ['', Validators.required],
          startDate: ['', Validators.required],
        });
        break;
      case 'it-ticket':
        this.requestForm = this.fb.group({
          issue: ['', Validators.required],
          priority: ['', Validators.required],
        });
        break;
      case 'profile-update':
        this.requestForm = this.fb.group({
          fieldToUpdate: ['', Validators.required],
          newValue: ['', Validators.required],
        });
        break;
      default:
        this.requestForm = this.fb.group({}); // Empty form for unknown types
        console.warn(`Unknown request type: ${this.requestType}`);
        break;
    }
  }

  onSubmit(): void {
    if (this.requestForm.valid) {
      this.isSubmitting = true;

      const requestData: CreateRequestDto = {
        type: this.getRequestTypeEnum(this.requestType),
        title: this.generateTitle(),
        description: this.generateDescription(),
        workflowId: this.getDefaultWorkflowId()
      };

      this.requestService.createRequest(requestData).pipe(
        takeUntil(this.destroy$)
      ).subscribe({
        next: (response) => {
          this.snackBar.open('Request submitted successfully!', 'Close', { duration: 3000 });
          this.router.navigate(['/requests']);
        },
        error: (error) => {
          console.error('Error submitting request:', error);
          this.snackBar.open('Error submitting request. Please try again.', 'Close', { duration: 3000 });
          this.isSubmitting = false;
        }
      });
    } else {
      this.markFormGroupTouched(this.requestForm);
    }
  }

  private getRequestTypeEnum(type: string): RequestType {
    switch (type) {
      case 'leave': return RequestType.Leave;
      case 'expense': return RequestType.Expense;
      case 'training': return RequestType.Training;
      case 'it-ticket': return RequestType.ITSupport;
      case 'profile-update': return RequestType.ProfileUpdate;
      default: return RequestType.Leave;
    }
  }

  private generateTitle(): string {
    const formValue = this.requestForm.value;
    switch (this.requestType) {
      case 'leave':
        return `Leave Request: ${formValue.startDate} to ${formValue.endDate}`;
      case 'expense':
        return `Expense Report: $${formValue.amount}`;
      case 'training':
        return `Training Request: ${formValue.courseName}`;
      case 'it-ticket':
        return `IT Support: ${formValue.issue}`;
      case 'profile-update':
        return `Profile Update: ${formValue.fieldToUpdate}`;
      default:
        return 'New Request';
    }
  }

  private generateDescription(): string {
    const formValue = this.requestForm.value;
    switch (this.requestType) {
      case 'leave':
        return `Leave request from ${formValue.startDate} to ${formValue.endDate}. Reason: ${formValue.reason}`;
      case 'expense':
        return `Expense report for $${formValue.amount}. Description: ${formValue.description}`;
      case 'training':
        return `Training request for ${formValue.courseName} by ${formValue.provider}, starting ${formValue.startDate}`;
      case 'it-ticket':
        return `IT Support ticket: ${formValue.issue}. Priority: ${formValue.priority}`;
      case 'profile-update':
        return `Profile update request to change ${formValue.fieldToUpdate} to ${formValue.newValue}`;
      default:
        return JSON.stringify(formValue);
    }
  }

  private getDefaultWorkflowId(): string {
    // Map request types to workflow names
    const workflowMapping: { [key: string]: string } = {
      'leave': 'Leave Request Workflow',
      'expense': 'Expense Request Workflow',
      'training': 'Training Request Workflow',
      'it-ticket': 'IT Support Workflow',
      'profile-update': 'Profile Update Workflow'
    };

    const workflowName = workflowMapping[this.requestType];
    if (workflowName) {
      const workflow = this.availableWorkflows.find(w => w.name === workflowName);
      if (workflow) {
        return workflow.id;
      }
    }

    // Fallback to first available workflow
    if (this.availableWorkflows.length > 0) {
      return this.availableWorkflows[0].id;
    }

    // Last resort - return empty GUID (this should not happen with seeded data)
    return '00000000-0000-0000-0000-000000000000';
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
}
