.notification-list-container {
  max-width: 500px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;

  h2 {
    text-align: center;
    color: #333;
    margin-bottom: 20px;
  }

  .no-notifications {
    text-align: center;
    color: #777;
    padding: 20px;
    border: 1px dashed #ccc;
    border-radius: 5px;
  }

  .notification-items {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      margin-bottom: 10px;
      background-color: #f9f9f9;
      border: 1px solid #eee;
      border-radius: 5px;
      transition: background-color 0.3s ease;

      &.read {
        background-color: #e9e9e9;
        color: #999;

        .notification-content p {
          text-decoration: line-through;
        }
      }

      .notification-content {
        flex-grow: 1;

        p {
          margin: 0;
          font-size: 1em;
          line-height: 1.4;
        }

        .timestamp {
          font-size: 0.8em;
          color: #a0a0a0;
        }
      }

      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: #0056b3;
        }
      }
    }
  }

  .clear-all-button {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1em;
    cursor: pointer;
    margin-top: 20px;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #c82333;
    }
  }
}
